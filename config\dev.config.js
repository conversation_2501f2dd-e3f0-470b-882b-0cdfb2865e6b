import proxy from './proxy';
import path from 'path';
// import SpeedMeasurePlugin from 'speed-measure-webpack-plugin';

export default {
  devtool: false,
  dva: {
    hmr: true,
  },
  proxy: proxy['dev'],
  nodeModulesTransform: {
    type: 'none',
    exclude: [],
  },
  // targets: {
  //   chrome: true,
  //   firefox: false,
  //   safari: false,
  //   edge: false,
  //   ios: false,
  // },
  chainWebpack: (memo, { webpack }) => {
    // memo.plugin('speed-measure-webpack-plugin').use(SpeedMeasurePlugin).end();
    memo.cache({
      type: 'filesystem',
      cacheDirectory: path.resolve(__dirname, 'node_modules/.cache'),
    });
  },
};

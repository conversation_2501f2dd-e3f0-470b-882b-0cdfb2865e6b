<svg width="85" height="54" viewBox="0 0 85 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<circle cx="42.598" cy="27" r="24" fill="#014176"/>
<circle cx="42.598" cy="27" r="23.5" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter1_d)">
<path d="M62.397 46.7991C64.997 44.1991 67.0595 41.1124 68.4666 37.7153C69.8737 34.3182 70.598 30.6772 70.598 27.0002C70.598 23.3231 69.8737 19.6821 68.4666 16.285C67.0595 12.8879 64.997 9.80121 62.397 7.20117" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter2_d)">
<path d="M22.7989 7.20085C20.1989 9.80089 18.1364 12.8876 16.7293 16.2847C15.3222 19.6818 14.5979 23.3228 14.5979 26.9998C14.5979 30.6769 15.3222 34.3179 16.7293 37.715C18.1364 41.1121 20.1989 44.1988 22.799 46.7988" stroke="#00C2FF"/>
</g>
<g filter="url(#filter3_d)">
<path d="M24 27L19.5 29.5981L19.5 24.4019L24 27Z" fill="#00C2FF"/>
</g>
<g filter="url(#filter4_d)">
<path d="M62 27L66.5 29.5981L66.5 24.4019L62 27Z" fill="#00C2FF"/>
</g>
<path d="M42.979 17.1789V13.5H41.1395V17.1789H42.979Z" fill="url(#paint0_linear)"/>
<path d="M47.9651 19.2311L49.8045 16.045L48.2115 15.1253L46.372 18.3113L47.9651 19.2311Z" fill="url(#paint1_linear)"/>
<path d="M54.2329 20.7064L51.0469 22.5459L50.1272 20.9529L53.3132 19.1134L54.2329 20.7064Z" fill="url(#paint2_linear)"/>
<path d="M33.436 22.5054L30.25 20.666L31.1697 19.0729L34.3558 20.9124L33.436 22.5054Z" fill="url(#paint3_linear)"/>
<path d="M34.6788 15.8427L36.5183 19.0288L38.1113 18.1091L36.2718 14.923L34.6788 15.8427Z" fill="url(#paint4_linear)"/>
<path d="M34.0421 27.9126C34.0421 23.4426 37.6657 19.8189 42.1357 19.8189C46.0979 19.8189 49.3952 22.6661 50.0932 26.4261L46.375 26.4261V26.8118C45.9769 26.4555 45.4513 26.2389 44.875 26.2389C43.6324 26.2389 42.625 27.2462 42.625 28.4889C42.625 29.4111 43.1799 30.2038 43.974 30.5512C43.1799 30.8986 42.625 31.6913 42.625 32.6136C42.625 33.5362 43.1804 34.3292 43.975 34.6764C43.1804 35.0236 42.625 35.8165 42.625 36.7392C42.625 37.3642 42.8798 37.9297 43.2913 38.3374H31.8347V35.3435H34.0421V27.9126Z" fill="url(#paint5_linear)"/>
<path d="M47.5 27.5511L55 27.5511V29.4261L47.5 29.4261V27.5511Z" fill="url(#paint6_linear)"/>
<path d="M47.5 31.6758L55 31.6758V33.5508L47.5 33.5508V31.6758Z" fill="url(#paint7_linear)"/>
<path d="M47.5 35.8014L55 35.8014V37.6764L47.5 37.6764V35.8014Z" fill="url(#paint8_linear)"/>
<path d="M46.375 34.2907C46.198 34.4491 45.9957 34.5799 45.775 34.6764C45.9957 34.7728 46.198 34.9036 46.375 35.0621V34.2907Z" fill="url(#paint9_linear)"/>
<path d="M45.776 30.5512C45.9963 30.6476 46.1982 30.7783 46.375 30.9365V30.1659C46.1982 30.3242 45.9963 30.4548 45.776 30.5512Z" fill="url(#paint10_linear)"/>
<path d="M44.875 27.3639C45.4963 27.3639 46 27.8675 46 28.4889C46 29.1102 45.4963 29.6139 44.875 29.6139C44.2537 29.6139 43.75 29.1102 43.75 28.4889C43.75 27.8675 44.2537 27.3639 44.875 27.3639Z" fill="url(#paint11_linear)"/>
<path d="M44.875 31.4886C45.4963 31.4886 46 31.9923 46 32.6136C46 33.2349 45.4963 33.7386 44.875 33.7386C44.2537 33.7386 43.75 33.2349 43.75 32.6136C43.75 31.9923 44.2537 31.4886 44.875 31.4886Z" fill="url(#paint12_linear)"/>
<path d="M44.875 35.6142C45.4963 35.6142 46 36.1178 46 36.7392C46 37.3605 45.4963 37.8642 44.875 37.8642C44.2537 37.8642 43.75 37.3605 43.75 36.7392C43.75 36.1178 44.2537 35.6142 44.875 35.6142Z" fill="url(#paint13_linear)"/>
<defs>
<filter id="filter0_d" x="16.598" y="1" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="40.598" y="4.84766" width="44.3051" height="44.3051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="0.292847" y="4.84766" width="44.3051" height="44.3051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="17.5" y="22.4023" width="8.5" height="9.19615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter4_d" x="60" y="22.4023" width="8.5" height="9.19615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint9_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint10_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint11_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint12_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint13_linear" x1="42.625" y1="13.5" x2="42.625" y2="38.3374" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
</defs>
</svg>

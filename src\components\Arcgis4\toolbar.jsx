import React, { useEffect, useState } from 'react';
import stylesIdx from './index.less';
import { amapLayer } from '@/components/Arcgis4/amapLayer';
import Extent from '@arcgis/core/geometry/Extent';
import Draw from '@arcgis/core/views/draw/Draw';
import Graphic from '@arcgis/core/Graphic';
import { Point, Polyline } from '@arcgis/core/geometry';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import * as geometryEngine from '@arcgis/core/geometry/geometryEngine';
import * as webMercatorUtils from '@arcgis/core/geometry/support/webMercatorUtils';
import TextSymbol from '@arcgis/core/symbols/TextSymbol';
import start from '@/assets/command/start.png';
import end from '@/assets/command/end.png';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import { Drawer, message } from 'antd';
import Circle from '@arcgis/core/geometry/Circle';
import { connect } from 'umi';
import { ROAD_STATUS } from '@/constants/mapConstants';

const PI = 3.1415926535897932384626; // 地图工具
const radius = 500;
function Toolbar(props) {
  const {
    arcgis: { toolbarLayers },
    dispatch,
    map,
    view,
    changeView,
    sceneView,
    body,
    zIndex,
    FAM_LAYER_3D,
    initMapData,
    closeMapDraw,
    setZIndex,
    showImage = false,
    changeShowImage = (a) => a,
    handleDraw,
  } = props;

  const [activeItem, setActiveItem] = useState(false);
  const [distance, setDistance] = useState('');
  const [duration, setDuration] = useState('');
  const [displayStr, setDisplayStr] = useState(false);
  const [instructions, setInstructions] = useState([]);
  const [analysisLayers, setAnalysisLayers] = useState([]);
  const [drawObj, setDrawObj] = useState();

  useEffect(() => {
    if (map) {
      const layer1 = map.findLayerById('layer1');
      const layer2 = map.findLayerById('layer2');
      const layer3 = map.findLayerById('layer3');
      const layer4 = map.findLayerById('layer4');
      const layer5 = map.findLayerById('layer5');
      const layer6 = map.findLayerById('layer6');
      const layer7 = map.findLayerById('layer7');
      if (!showImage) {
        layer1.visible = true;
        layer2.visible = true;
        layer3.visible = false;
        layer4.visible = false;
        if (layer5) {
          layer5.visible = true;
        }
        if (layer6) {
          layer6.visible = true;
        }
        if (layer7) {
          layer7.visible = false;
        }
      } else {
        layer1.visible = false;
        layer2.visible = false;
        layer3.visible = true;
        layer4.visible = true;
        if (layer5) {
          layer5.visible = false;
        }
        if (layer6) {
          layer6.visible = true;
        }
        if (layer7) {
          layer7.visible = true;
        }
      }
    }
  }, [map, showImage]);

  /**
   * 矢量地图、卫星图层切换
   */
  const changeMapType = () => {
    changeShowImage(!showImage);
  };

  /**
   * 二维三维切换
   */
  const changeSceneView = (e) => {
    e.stopPropagation();
    e.preventDefault();
    closeMapDraw();
    clearToolbarLayer();
    setActiveItem(false);
    changeView(!sceneView);
  };

  /**
   * 实时交通
   * @returns {Promise<void>}
   */
  const onLiveTraffic = async () => {
    if (map) {
      if (activeItem) {
        const layer5 = map.findLayerById('traffic');
        if (layer5) {
          map.remove(layer5);
        }
      } else {
        const layer5 = amapLayer();
        layer5.id = 'traffic';
        map.layers.add(layer5);
      }
      setActiveItem(!activeItem);
    }
  };
  /**
   * 放大
   */
  const onExpand = () => {
    if (view) {
      view.zoom++;
    }
  };
  /**
   * 缩小
   */
  const onReduce = () => {
    if (view) {
      view.zoom--;
    }
  };

  /**
   * 全图处理
   */
  const onFull = () => {
    if (view && initMapData.xmin && initMapData.ymin && initMapData.xmax && initMapData.ymax) {
      const fullExtent = new Extent({
        xmin: initMapData.xmin,
        ymin: initMapData.ymin,
        xmax: initMapData.xmax,
        ymax: initMapData.ymax,
        spatialReference: { wkid: 4326 },
      });
      view.extent = fullExtent;
    }
  };

  /**
   * 计算距离
   * @param line 线
   * @param txtLayer 文本图层，用来显示距离文本
   */
  function calLength(line, txtLayer) {
    if (view.spatialReference.isWebMercator) {
      // eslint-disable-next-line no-param-reassign
      line = webMercatorUtils.webMercatorToGeographic(line);
    }
    let length = geometryEngine.geodesicLength(line, 'kilometers');
    const content = length.toFixed(3) + '公里';
    const verties = line.paths;
    // console.log(verties[0][0][0],verties[0][0][1]);
    const txtPt = new Point({
      longitude: verties[0][0][0],
      latitude: verties[0][0][1],
      spatialReference: view.spatialReference,
    });
    createTextGraphic(content, txtPt, txtLayer);
  }

  /**
   * 计算面积
   * @param geom     几何图形
   * @param txtLayer 文本图层，用来显示距离文本
   */
  function calArea(geom, txtLayer) {
    if (geom.rings[0].length > 2) {
      if (view.spatialReference.isWebMercator) {
        // eslint-disable-next-line no-param-reassign
        geom = webMercatorUtils.webMercatorToGeographic(geom);
      }
      let area = geometryEngine.geodesicArea(geom, 'square-kilometers');
      if (area < 0) {
        const simplifiedPolygon = geometryEngine.simplify(geom);
        if (simplifiedPolygon) {
          area = geometryEngine.geodesicArea(simplifiedPolygon, 'square-kilometers');
        }
      }
      const content = area.toFixed(3) + '平方公里';
      createTextGraphic(content, geom.centroid, txtLayer);
    }
  }

  /**
   * 生成显示的文本
   * @param content   文本内容
   * @param point     文本显示位置
   * @param txtLayer  文本图层
   */
  function createTextGraphic(content, point, txtLayer) {
    txtLayer.removeAll();
    const txtSym = new TextSymbol();
    txtSym.text = content;
    txtSym.color = [255, 0, 0];
    txtSym.font = { size: 10 };
    const txtGp = new Graphic({
      geometry: point,
      symbol: txtSym,
    });
    txtLayer.add(txtGp);
  }

  /**
   * 量面
   */
  const onArea = () => {
    handleDraw(true);
    if (drawObj) {
      drawObj?.reset();
    }
    if (view) {
      if (body) {
        body.current.style.cursor = 'crosshair';
      }
      let draw = new Draw({
        view: view,
      });
      let action = draw.create('polyline', { mode: 'click' });
      const graphicsLayer = new GraphicsLayer(); //{sss:'area'}
      const txtLayer = new GraphicsLayer();
      //console.log(toolbarLayers)

      map.layers.addMany([graphicsLayer, txtLayer]);
      addToolbarLayer(graphicsLayer);
      addToolbarLayer(txtLayer);
      action.on(
        ['vertex-add', 'vertex-remove', 'cursor-update', 'draw-complete'],
        function (event) {
          createPolygonGraphic(event, graphicsLayer, txtLayer);
          if (event.type === 'draw-complete') {
            if (body) {
              body.current.style.cursor = 'default';
            }
          }
        },
      );
      setDrawObj(draw);
    }

    function createPolygonGraphic(event, graphicsLayer, txtLayer) {
      const vertices = event.vertices;
      graphicsLayer.removeAll();
      if (vertices.length >= 2) {
        let polygon = {
          type: 'polygon', // autocasts as Polygon
          rings: vertices,
          spatialReference: view.spatialReference,
        };

        let graphic = new Graphic({
          geometry: polygon,
          symbol: {
            type: 'simple-fill', // autocasts as SimpleFillSymbol
            color: 'rgba(255,0,0,0.2)',
            style: 'solid',
            outline: {
              // autocasts as SimpleLineSymbol
              color: '#ff5502',
              width: 1,
            },
          },
        });
        graphicsLayer.add(graphic);
        //console.log(graphicsLayer)
        calArea(graphic.geometry, txtLayer);
      }
    }
  };

  /**
   * 量距
   */
  const onDistance = () => {
    handleDraw(true);
    if (drawObj) {
      drawObj?.reset();
    }
    if (view) {
      if (body) {
        body.current.style.cursor = 'crosshair';
      }
      let draw = new Draw({
        view: view,
      });
      let action = draw.create('polyline', { mode: 'click' });
      const graphicsLayer = new GraphicsLayer();
      const txtLayer = new GraphicsLayer();
      map.layers.addMany([graphicsLayer, txtLayer]);
      addToolbarLayer(graphicsLayer);
      addToolbarLayer(txtLayer);
      action.on(
        ['vertex-add', 'vertex-remove', 'cursor-update', 'draw-complete'],
        function (event) {
          createPolylineGraphic(event, graphicsLayer, txtLayer);
          if (event.type === 'draw-complete') {
            if (body) {
              body.current.style.cursor = 'default';
            }
          }
        },
      );
      setDrawObj(draw);
    }
    function createPolylineGraphic(event, graphicsLayer, txtLayer) {
      const vertices = event.vertices;
      graphicsLayer.removeAll();
      if (vertices.length >= 2) {
        const line = new Polyline({
          paths: vertices,
          spatialReference: view.spatialReference,
        });
        // 生成绘制的图形
        const graphic = new Graphic({
          geometry: line,
          symbol: {
            type: 'simple-line', // autocasts as new SimpleFillSymbol
            color: '#ff5502',
            width: 1,
            cap: 'round',
            join: 'round',
          },
        });
        graphicsLayer.add(graphic);
        calLength(line, txtLayer);
      }
    }
  };

  /**
   * 清除
   */
  const clearToolbarLayer = () => {
    handleDraw(false);
    if (drawObj) {
      drawObj?.reset();
    }
    setDrawObj(undefined);
    toolbarLayers.forEach((layer) => {
      map.remove(layer);
      removeToolbarLayer(layer);
    });
    if (body) {
      body.current.style.cursor = 'default';
    }
    setDisplayStr(false);
    setAnalysisLayers([]);
  };

  /**
   * 路径分析
   * @param startPoint
   */
  const analysis = (startPoint) => {
    if (drawObj) {
      drawObj?.reset();
    }
    if (view) {
      if (!startPoint && analysisLayers.length > 0) {
        analysisLayers.forEach((layer) => {
          map.remove(layer);
          removeToolbarLayer(layer);
        });
        setAnalysisLayers([]);
        setDisplayStr(false);
      }
      if (body) {
        body.current.style.cursor = 'crosshair';
      }
      let draw = new Draw({
        view: view,
      });
      let action = draw.create('point', { mode: 'click' });
      const graphicsLayer = new GraphicsLayer();
      map.layers.add(graphicsLayer);
      analysisLayers.push(graphicsLayer);
      setAnalysisLayers([...analysisLayers]);
      addToolbarLayer(graphicsLayer);
      action.on(['draw-complete'], function (event) {
        const point = createPointGraphic(event, graphicsLayer, startPoint ? end : start);
        if (!startPoint) {
          // 起点为空
          analysis(point);
        } else {
          body.current.style.cursor = 'default';
          debugger
          // 添加预定义路径数据到地图
          addPredefinedPath(graphicsLayer);
          // 获得起点、终点，开始路径分析
          analysisResult(startPoint, point, graphicsLayer);
        }
      });
      setDrawObj(draw);
    }
    function createPointGraphic(event, graphicsLayer, image) {
      const vertices = event.vertices;
      const ps = new PictureMarkerSymbol({
        url: image,
        height: 24,
        width: 14,
        yoffset: 12,
      });
      const point = new Point();
      point.x = vertices[0][0];
      point.y = vertices[0][1];
      point.spatialReference = view.spatialReference;

      const graphic = new Graphic({
        geometry: point,
        symbol: ps,
      });
      graphicsLayer.add(graphic);
      return {
        longitude: point.longitude,
        latitude: point.latitude,
      };
    }
    // 路径分析路线
    function analysisResult(startPoint, endPoint, layer) {
      dispatch({
        type: 'util/guideRoad',
        payload: {
          lon: startPoint.longitude,
          lat: startPoint.latitude,
          toLon: endPoint.longitude,
          toLat: endPoint.latitude,
        },
      }).then((res) => {
        if (res?.code === '00') {
          const data = res.data || {};
          setDuration(timezh(data.duration));
          setDistance(data.distance);
          setInstructions(data.instructions);
          const steps = JSON.parse(data.navigateTrail || []);
          if (steps?.length) {
            let prevPoint = [];
            steps.map((lines) => {
              lines.map((item) => {
                const { line, status } = item;
                const paths = line.split(';').map((point) => {
                  if (sceneView) {
                    return [...point.split(','), 0];
                  } else {
                    return point.split(',');
                  }
                });
                const polyline = {
                  type: 'polyline',
                  paths,
                };
                const [firstPoint, ...restPoints] = paths;
                if (
                  prevPoint?.length &&
                  (firstPoint[0] !== prevPoint[0] || firstPoint[1] !== prevPoint[1])
                ) {
                  const outGraphic = new Graphic({
                    geometry: {
                      type: 'polyline',
                      paths: [prevPoint, firstPoint],
                    },
                    symbol: {
                      type: 'simple-line',
                      color: ROAD_STATUS[status].outlineColor,
                      style: 'solid',
                      width: '5px',
                      join: 'round',
                      cap: 'square',
                    },
                  });
                  layer.add(outGraphic);
                }
                prevPoint = paths[paths.length - 1];
                const outGraphic = new Graphic({
                  geometry: polyline,
                  symbol: {
                    type: 'simple-line',
                    color: ROAD_STATUS[status].outlineColor,
                    style: 'solid',
                    width: '5px',
                    join: 'round',
                    cap: 'square',
                  },
                });
                layer.add(outGraphic);
              });
            });
          }
          // setDisplayStr(true);
        }
      });
    }

    // 添加预定义路径数据到地图
    function addPredefinedPath(layer) {
      debugger
      // 预定义的导航数据
      const predefinedData = {
        "duration": null,
        "instructions": null,
        "distance": 871,
        "navigateTrail": "[[{\"line\":\"110.44136054,19.9430273;110.44118478,19.94301979;110.44118441,19.94312632;110.44118422,19.94318321;110.4411811,19.94320798;110.44118114,19.94356156;110.44117996,19.94390883;110.44117913,19.94437723;110.44117859,19.94459252;110.4411774,19.94499108;110.44117567,19.94563615;110.44144412,19.94563429;110.44160132,19.94563924;110.44210841,19.94565522;110.44401308,19.94565779;110.44455297,19.94568386;110.44702172,19.94569188;110.44716408,19.94569037;110.44730619,19.94568207;110.44744773,19.94566701;110.44758837,19.9456452;110.44779238,19.94560275;110.44799539,19.94555576;110.44819728,19.94550428;110.44839797,19.94544832;110.44895977,19.9453115;110.44903428,19.94530274;110.44919867,19.94527516;110.44943361,19.94524406;110.44954686,19.94523538;110.44999388,19.94523041;110.45128788,19.94522911;110.45160678,19.94525067;110.45258136,19.94526181;110.45262566,19.94525934;110.45288834,19.94524466;110.45326686,19.94523659;110.45529365,19.94524729;110.45589551,19.9452089;110.45619084,19.94521072;110.45641439,19.94522019;110.45652925,19.94522126;110.45676056,19.94523372;110.45688933,19.94525547;110.45702417,19.94527101;110.45718513,19.94529522;110.45744149,19.94534942;110.45793928,19.94546673;110.45820458,19.94553584;110.45834767,19.94556299;110.4585079,19.94555769;110.45869124,19.94560629;110.45887624,19.9456481;110.45906263,19.94568309;110.45925017,19.94571118;110.45934496,19.94572183;110.45943862,19.94573235;110.4596277,19.94574658;110.45981716,19.94575383;110.46012139,19.94575556;110.46109752,19.94576113;110.46691489,19.945782;110.46699719,19.94576078;110.46708664,19.94574317;110.46703751,19.94570669;110.4669359,19.94566641;110.46687231,19.94565908;110.46687307,19.94533986;110.46759139,19.94534348;110.46759256,19.94496162;110.46759346,19.94447463;110.46759472,19.94398003;110.46759451,19.94354996;110.46759513,19.94339751;110.4675887,19.94335866;110.46732897,19.94335594\",\"status\":\"畅通\"}]]",
        "roads": null,
        "traffic_lights": null
      };

      // 解析导航路径数据
      const steps = JSON.parse(predefinedData.navigateTrail || []);
      if (steps?.length) {
        let prevPoint = [];
        steps.map((lines) => {
          lines.map((item) => {
            const { line, status } = item;
            const paths = line.split(';').map((point) => {
              if (sceneView) {
                return [...point.split(','), 0];
              } else {
                return point.split(',');
              }
            });
            const polyline = new Polyline({
              paths,
              spatialReference: { wkid: 4326 },
            });
            const [firstPoint, ...restPoints] = paths;
            if (
              prevPoint?.length &&
              (firstPoint[0] !== prevPoint[0] || firstPoint[1] !== prevPoint[1])
            ) {
              const connectionPolyline = new Polyline({
                paths: [prevPoint, firstPoint],
                spatialReference: { wkid: 4326 },
              });
              const outGraphic = new Graphic({
                geometry: connectionPolyline,
                symbol: {
                  type: 'simple-line',
                  color: ROAD_STATUS["缓行"].outlineColor,
                  style: 'solid',
                  width: '5px',
                  join: 'round',
                  cap: 'square',
                },
              });
              layer.add(outGraphic);
            }
            prevPoint = paths[paths.length - 1];
            const outGraphic = new Graphic({
              geometry: polyline,
              symbol: {
                type: 'simple-line',
                color: ROAD_STATUS["缓行"].outlineColor,
                style: 'solid',
                width: '5px',
                join: 'round',
                cap: 'square',
              },
            });
            layer.add(outGraphic);
          });
        });
      }
    }
  };

  /**
   * 增加工具栏图层 state
   * @param layer
   * @returns {Promise<void>}
   */
  const addToolbarLayer = async (layer) => {
    toolbarLayers.push(layer);
    await dispatch({
      type: 'arcgis/putData',
      payload: {
        toolbarLayers: toolbarLayers,
      },
    });
  };

  /**
   * 移除工具栏图层 state
   * @param layer
   * @returns {Promise<void>}
   */
  const removeToolbarLayer = async (layer) => {
    const nLayers = toolbarLayers.filter((item) => item.id != layer.id);
    await dispatch({
      type: 'arcgis/putData',
      payload: {
        toolbarLayers: nLayers,
      },
    });
  };

  /**
   * 坐标系转换
   * @param lat
   * @param lon
   * @returns {{lon: *, lat: number}}
   */
  const delta = (lat, lon) => {
    const a = 6378245.0; //  a: 卫星椭球坐标投影到平面地图坐标系的投影因子。

    const ee = 0.00669342162296594323; //  ee: 椭球的偏心率。

    let dLat = transformLat(lon - 105.0, lat - 35.0);
    let dLon = transformLon(lon - 105.0, lat - 35.0);
    const radLat = (lat / 180.0) * PI;
    let magic = Math.sin(radLat);
    magic = 1 - ee * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * PI);
    dLon = (dLon * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * PI);
    return {
      lat: dLat,
      lon: dLon,
    };
  };

  const transformLat = (x, y) => {
    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
    ret += ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(y * PI) + 40.0 * Math.sin((y / 3.0) * PI)) * 2.0) / 3.0;
    ret += ((160.0 * Math.sin((y / 12.0) * PI) + 320 * Math.sin((y * PI) / 30.0)) * 2.0) / 3.0;
    return ret;
  };

  const transformLon = (x, y) => {
    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
    ret += ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(x * PI) + 40.0 * Math.sin((x / 3.0) * PI)) * 2.0) / 3.0;
    ret += ((150.0 * Math.sin((x / 12.0) * PI) + 300.0 * Math.sin((x / 30.0) * PI)) * 2.0) / 3.0;
    return ret;
  };

  /**
   * 时间格式转换
   * @param value
   * @returns {string}
   */
  const timezh = (value) => {
    let theTime = parseInt(value); // 需要转换的时间秒
    let theTime1 = 0; // 分
    let theTime2 = 0; // 小时
    let theTime3 = 0; // 天
    if (theTime > 60) {
      theTime1 = parseInt(theTime / 60);
      theTime = parseInt(theTime % 60);
      if (theTime1 > 60) {
        theTime2 = parseInt(theTime1 / 60);
        theTime1 = parseInt(theTime1 % 60);
        if (theTime2 > 24) {
          // 大于24小时
          theTime3 = parseInt(theTime2 / 24);
          theTime2 = parseInt(theTime2 % 24);
        }
      }
    }
    let result = '';
    if (theTime > 0) {
      result = '' + parseInt(theTime) + '秒';
    }
    if (theTime1 > 0) {
      result = '' + parseInt(theTime1) + '分' + result;
    }
    if (theTime2 > 0) {
      result = '' + parseInt(theTime2) + '小时' + result;
    }
    if (theTime3 > 0) {
      result = '' + parseInt(theTime3) + '天' + result;
    }
    return result;
  };

  const changeZIndex = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setZIndex();
    closeMapDraw();
  };

  return (
    <>
      <div
        className={stylesIdx.toolbars}
        style={{ zIndex: zIndex }}
        onClick={changeZIndex}
        onContextMenu={(e) => {
          e.preventDefault();
          e.stopPropagation();
          closeMapDraw();
        }}
        onDoubleClick={(e) => {
          e.stopPropagation();
          closeMapDraw();
        }}
      >
        <div className={stylesIdx.toolbar_content}>
          <div className={stylesIdx.toolbar_Item} onClick={changeMapType}>
            <em className={stylesIdx.icon_map} />
            {showImage ? (
              <span className={stylesIdx.text}>矢量地图</span>
            ) : (
              <span className={stylesIdx.text}>影像地图</span>
            )}
          </div>
          {/*{FAM_LAYER_3D && (
            <div className={stylesIdx.toolbar_Item} onClick={changeSceneView}>
              <em className={stylesIdx.icon_3D} />
              {sceneView ? (
                <span className={stylesIdx.text}>二维地图</span>
              ) : (
                <span className={stylesIdx.text}>三维地图</span>
              )}
            </div>
          )}
          {!sceneView && (
            <div
              className={[stylesIdx.toolbar_Item, activeItem ? stylesIdx.activeItem : ''].join(' ')}
              onClick={onLiveTraffic}
            >
              <em className={stylesIdx.icon_tra} />
              <span className={stylesIdx.text}>实时交通</span>
            </div>
          )} */}
          <div className={stylesIdx.toolbar_Item} onClick={onExpand}>
            <em className={stylesIdx.icon_fd} />
            <span className={stylesIdx.text}>放大</span>
          </div>
          <div className={stylesIdx.toolbar_Item} onClick={onReduce}>
            <em className={stylesIdx.icon_sx} />
            <span className={stylesIdx.text}>缩小</span>
          </div>
          <div className={stylesIdx.toolbar_Item} onClick={onFull}>
            <em className={stylesIdx.icon_qt} />
            <span className={stylesIdx.text}>全图</span>
          </div>
          <div className={stylesIdx.toolbar_Item} id="area" onClick={onArea}>
            <em className={stylesIdx.icon_lm} />
            <span className={stylesIdx.text}>量面</span>
          </div>
          <div className={stylesIdx.toolbar_Item} id="distance" onClick={onDistance}>
            <em className={stylesIdx.icon_lj} />
            <span className={stylesIdx.text}>量距</span>
          </div>
          <div className={stylesIdx.toolbar_Item} id="distance" onClick={() => analysis()}>
            <em className={stylesIdx.icon_ljfx} />
            <span className={stylesIdx.text}>路径分析</span>
          </div>
          <div className={stylesIdx.toolbar_Item} onClick={clearToolbarLayer}>
            <em className={stylesIdx.icon_qc} />
            <span className={stylesIdx.text}>清除</span>
          </div>
        </div>
      </div>
      <Drawer
        title={`路程:${distance}米, 预计时间:${duration}；`}
        placement="right"
        getContainer={false}
        mask={false}
        onClose={() => {
          setDisplayStr(false);
        }}
        className={stylesIdx.drawer_1}
        open={displayStr}
        width={372}
      >
        {instructions && instructions.length
          ? instructions.map((item, index) => (
              <p key={`key${index}`}>
                {index + 1}、{item}
              </p>
            ))
          : ''}
      </Drawer>
    </>
  );
}
export default connect(({ arcgis, util }) => ({
  arcgis,
  util,
}))(Toolbar);

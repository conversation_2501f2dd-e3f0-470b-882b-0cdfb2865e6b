<svg width="81" height="54" viewBox="0 0 81 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2716_51001)">
<circle cx="40.0977" cy="27" r="24" fill="#014176"/>
<circle cx="40.0977" cy="27" r="23.5" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter1_d_2716_51001)">
<path d="M59.8984 46.8011C62.4985 44.2011 64.5609 41.1144 65.9681 37.7172C67.3752 34.3201 68.0994 30.6791 68.0994 27.0021C68.0994 23.3251 67.3752 19.6841 65.9681 16.287C64.5609 12.8899 62.4985 9.80317 59.8984 7.20313" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter2_d_2716_51001)">
<path d="M20.3008 7.19889C17.7007 9.79893 15.6383 12.8856 14.2311 16.2827C12.824 19.6799 12.0998 23.3209 12.0998 26.9979C12.0998 30.6749 12.824 34.3159 14.2311 37.713C15.6383 41.1101 17.7007 44.1968 20.3008 46.7969" stroke="#00C2FF"/>
</g>
<g filter="url(#filter3_d_2716_51001)">
<path d="M21.5 27L17 29.5981L17 24.4019L21.5 27Z" fill="#00C2FF"/>
</g>
<g filter="url(#filter4_d_2716_51001)">
<path d="M59.5 27L64 29.5981L64 24.4019L59.5 27Z" fill="#00C2FF"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.8359 18.7214C28.8359 18.3057 29.1729 17.9688 29.5886 17.9688H46.9005C47.3162 17.9688 47.6532 18.3057 47.6532 18.7214V22.7973L52.1686 20.6042V33.3999L47.6532 31.2067V35.2806C47.6532 35.6963 47.3162 36.0333 46.9005 36.0333H29.5886C29.1729 36.0333 28.8359 35.6963 28.8359 35.2806V18.7214ZM42.8182 26.6103C43.1212 26.7852 43.1212 27.2226 42.8182 27.3976L36.6818 30.9404C36.3788 31.1153 36 30.8966 36 30.5467L36 23.4611C36 23.1112 36.3788 22.8925 36.6818 23.0674L42.8182 26.6103Z" fill="url(#paint0_linear_2716_51001)"/>
<defs>
<filter id="filter0_d_2716_51001" x="14.0977" y="1" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2716_51001"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2716_51001" result="shape"/>
</filter>
<filter id="filter1_d_2716_51001" x="57.543" y="4.84766" width="13.0547" height="44.3086" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2716_51001"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2716_51001" result="shape"/>
</filter>
<filter id="filter2_d_2716_51001" x="9.60156" y="4.84375" width="13.0547" height="44.3086" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2716_51001"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2716_51001" result="shape"/>
</filter>
<filter id="filter3_d_2716_51001" x="15" y="22.4023" width="8.5" height="9.19531" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2716_51001"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2716_51001" result="shape"/>
</filter>
<filter id="filter4_d_2716_51001" x="57.5" y="22.4023" width="8.5" height="9.19531" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2716_51001"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2716_51001" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2716_51001" x1="40.5023" y1="17.9688" x2="40.5023" y2="36.0333" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
</defs>
</svg>

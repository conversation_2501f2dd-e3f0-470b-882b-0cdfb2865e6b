{"version": 3, "file": "external_api.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAA8B,qBAAID,IAElCD,EAA2B,qBAAIC,GAChC,CATD,CASGK,MAAM,I,qFCJT,MAAMC,UAA0BC,EAAa,c,QAAA,oB,EAK9B,CAAC,G,EAL6B,mB,sBAAA,K,uDAAA,K,IAAA,CAYzCC,QACIC,KAAKC,SAAW,CAAC,CACpB,CAOGC,aACA,OAAOC,OAAOC,KAAKJ,KAAKC,UAAUC,MACrC,CAQDG,QAAQC,GACJ,OAAON,KAAKC,SAASK,EACxB,CAUDC,QAAQD,EAASE,GACbR,KAAKC,SAASK,GAAWE,CAC5B,CAQDC,WAAWH,UACAN,KAAKC,SAASK,EACxB,CAQDI,IAAIC,GACA,MAAMP,EAAOD,OAAOC,KAAKJ,KAAKC,UAE9B,KAAIG,EAAKF,QAAUS,GAInB,OAAOP,EAAKO,EACf,CAQDC,YAAuB,IAAbC,EAAa,uDAAJ,GACf,GAAsB,IAAlBA,EAAOX,OACP,OAAOY,KAAKC,UAAUf,KAAKC,UAG/B,MAAMe,EAAc,IAAKhB,KAAKC,UAM9B,OAJAY,EAAOI,SAAQP,WACJM,EAAYN,EAAnB,IAGGI,KAAKC,UAAUC,EACzB,EAgIE,MAAME,EAAoB,IA1HjC,cAAgCpB,EAK5BqB,cACIC,QAEA,IACIpB,KAAKC,SAAWoB,OAAOC,aACvBtB,KAAKuB,uBAAwB,CAGhC,CAFC,MAAOV,GAER,CAEIb,KAAKC,WACNuB,QAAQC,KAAK,8BACbzB,KAAKC,SAAW,IAAIJ,EACpBG,KAAKuB,uBAAwB,EAEpC,CAODG,yBACI,OAAO1B,KAAKuB,qBACf,CAODxB,QACIC,KAAKC,SAASF,QACdC,KAAK2B,KAAK,UACb,CAOGzB,aACA,OAAOF,KAAKC,SAASC,MACxB,CASDG,QAAQC,GACJ,OAAON,KAAKC,SAASI,QAAQC,EAChC,CASDC,QAAQD,EAASE,GAAwC,IAA9BoB,EAA8B,wDACrD5B,KAAKC,SAASM,QAAQD,EAASE,GAE1BoB,GACD5B,KAAK2B,KAAK,UAEjB,CAMDlB,WAAWH,GACPN,KAAKC,SAASQ,WAAWH,GACzBN,KAAK2B,KAAK,UACb,CASDjB,IAAImB,GACA,OAAO7B,KAAKC,SAASS,IAAImB,EAC5B,CAQDjB,YAAuB,IAAbC,EAAa,uDAAJ,GACf,GAAIb,KAAK0B,yBACL,OAAO1B,KAAKC,SAASW,UAAUC,GAGnC,MAAMX,EAASF,KAAKC,SAASC,OACvB4B,EAAsB,CAAC,EAE7B,IAAK,IAAID,EAAI,EAAGA,EAAI3B,EAAQ2B,IAAK,CAC7B,MAAMnB,EAAMV,KAAKC,SAASS,IAAImB,GAEzBhB,EAAOkB,SAASrB,KACjBoB,EAAoBpB,GAAOV,KAAKC,SAASI,QAAQK,GAExD,CAED,OAAOI,KAAKC,UAAUe,EACzB,G,aCrNL,MAAME,EAAY,CAAC,YAAa,cAAe,aCHxC,IAAIC,GACX,SAAWA,GACPA,EAAWA,EAA4B,gBAAI,KAAO,iBACrD,CAFD,CAEGA,IAAeA,EAAa,CAAC,IAgEhC,SAASC,EAAoBC,GACzB,MAAMC,EAAQ,IAAIC,OAAO,6BAA4B,MAC/CC,EAAQF,EAAMG,KAAKJ,GACzB,GAAIG,EAAO,CAGP,IAAIE,EAAWF,EAAMA,EAAMpC,OAAS,GAAGuC,cACtB,UAAbD,GAAqC,WAAbA,IACxBA,EAAW,WAGfL,EAAMA,EAAIO,UAAUN,EAAMO,YAClBC,WAAW,QAGfT,EAAMK,EAAWL,EAGzB,CACA,OAAOA,CACX,CA6EA,SAASU,EAAwBC,EAAM,CAAC,GACpC,MAAMC,EAAS,GACf,IAAK,MAAMrC,KAAOoC,EACd,IACIC,EAAOC,KAAK,GAAGtC,KAAOuC,mBAAmBnC,KAAKC,UAAU+B,EAAIpC,OAIhE,CAFA,MAAOwC,GACH1B,QAAQC,KAAK,kBAAkBf,MAAQwC,IAC3C,CAEJ,OAAOH,CACX,CAkBO,SAASI,EAAuBC,GAEnC,MAAMN,EAAM,CACRO,SAAUC,GAEd,IAAIlB,EACAE,EAsCAiB,EArBJ,GAXAH,EAAMA,EAAII,QAAQ,MAAO,IAEzBpB,EAAQ,IAAIC,OAxKoB,4BAwKS,MACzCC,EAAQF,EAAMG,KAAKa,GACfd,IACAQ,EAAIN,SAAWF,EAAM,GAAGG,cACxBW,EAAMA,EAAIV,UAAUN,EAAMO,YAG9BP,EAAQ,IAAIC,OAAO,eAA8B,MACjDC,EAAQF,EAAMG,KAAKa,GACfd,EAAO,CACP,IAAImB,EAAYnB,EAAM,GAAGI,UAAmB,GAC5CU,EAAMA,EAAIV,UAAUN,EAAMO,WAE1B,MAAMe,EAAmBD,EAAUE,QAAQ,MACjB,IAAtBD,IACAD,EAAYA,EAAUf,UAAUgB,EAAmB,IAEvDZ,EAAIc,KAAOH,EAEX,MAAMI,EAAiBJ,EAAUK,YAAY,MACrB,IAApBD,IACAf,EAAIiB,KAAON,EAAUf,UAAUmB,EAAiB,GAChDJ,EAAYA,EAAUf,UAAU,EAAGmB,IAGvCf,EAAIkB,SAAWP,CACnB,CAiBA,GAfArB,EAAQ,IAAIC,OAAO,YAAyB,MAC5CC,EAAQF,EAAMG,KAAKa,GAEfd,IACAiB,EAAWjB,EAAM,GACjBc,EAAMA,EAAIV,UAAUN,EAAMO,YAE1BY,EACAA,EAASX,WAAW,OAASW,EAAW,IAAIA,KAG5CA,EAAW,IAEfT,EAAIS,SAAWA,EAEXH,EAAIR,WAAW,KAAM,CACrB,IAAIqB,EAAiBb,EAAIO,QAAQ,IAAK,IACd,IAApBM,IACAA,EAAiBb,EAAIlD,QAEzB4C,EAAIoB,OAASd,EAAIV,UAAU,EAAGuB,GAC9Bb,EAAMA,EAAIV,UAAUuB,EACxB,MAEInB,EAAIoB,OAAS,GAKjB,OAFApB,EAAIqB,KAAOf,EAAIR,WAAW,KAAOQ,EAAM,GAEhCN,CACX,CAqDA,SAASQ,EAAqBc,GAG1B,MAAM,KAAED,EAAI,KAAEP,EAAI,SAAEL,EAAQ,SAAEf,EAAQ,OAAE0B,GAAWE,GAAQpE,KAC3D,IAAIoD,EAAM,GAOV,OANAZ,IAAaY,GAAOZ,GAEpBoB,IAASR,GAAO,KAAKQ,KACrBR,GAAOG,GAAY,IACnBW,IAAWd,GAAOc,GAClBC,IAASf,GAAOe,GACTf,CACX,CA0DO,SAASiB,EAAkBC,GAG9B,IAAIC,EAEAA,EADAD,EAAEE,WAAaF,EAAEG,KACX,IAAIC,IAAIJ,EAAEG,KAAMH,EAAEE,WAAWnB,WAE9BiB,EAAEG,KACDH,EAAEG,KAGFH,EAAEK,KAAO,GAEnB,MAAMA,EAAMxB,EAAuBjB,EAAoBqC,IAEvD,IAAKI,EAAInC,SAAU,CACf,IAAIA,EAAW8B,EAAE9B,UAAY8B,EAAEM,OAC3BpC,IAGAA,EAASqC,SAAS,OAASrC,GAAY,KACvCmC,EAAInC,SAAWA,EAEvB,CAEA,IAAI,SAAEe,GAAaoB,EACnB,IAAKA,EAAIf,KAAM,CAKX,MAAMkB,EAASR,EAAEQ,QAAUR,EAAEV,MAAQU,EAAEN,SACvC,GAAIc,EAAQ,CACR,MAAM,KAAElB,EAAI,SAAEI,EAAUT,SAAUwB,EAAW,KAAEhB,GAASZ,EAIxDjB,EAAoB,oBAAuB4C,MAEvClB,IACAe,EAAIf,KAAOA,EACXe,EAAIX,SAAWA,EACfW,EAAIZ,KAAOA,GAGF,MAAbR,GAAoC,MAAhBwB,IAAwBxB,EAAWwB,EAC3D,CACJ,CAGA,MAAMN,EAAOH,EAAEU,UAAYV,EAAEG,MACzBA,IACIE,EAAIpB,SAASsB,SAAS,MAClBF,EAAIpB,SAASsB,SAAS,IAAIJ,OAClClB,EAASsB,SAAS,OAAStB,GAAY,KACvCA,GAAYkB,GAEhBE,EAAIpB,SAAWA,EAGf,MAAM,IAAE0B,EAAG,KAAEC,EAAI,QAAEC,GAAYb,EACzBJ,EAAS,IAAIkB,gBAAgBT,EAAIT,QACnCe,GACAf,EAAOmB,IAAI,MAAOJ,GAEtB,MAAM,gBAAEK,GAAoBhB,EAAEiB,iBAAmB,CAAC,GAC9CL,GAAQI,IACRpB,EAAOmB,IAAI,OAAQH,GAAQI,GAE3BH,GACAjB,EAAOmB,IAAI,UAAWF,GAE1B,MAAMK,EAAetB,EAAOb,WACxBmC,IACAb,EAAIT,OAAS,IAAIsB,KAGrB,IAAI,KAAErB,GAASQ,EACf,IAAK,MAAMc,IAAa,CAAC,SAAU,kBAAmB,UAAW,WAAY,WAAY,CACrF,MAAMC,EAAiB7C,EAAwByB,EAAE,GAAGmB,eAC7CnB,EAAEmB,IACFnB,EAAE,GAAGmB,cACZ,GAAIC,EAAexF,OAAQ,CACvB,IAAIyF,EAAkB,GAAGF,KAAaC,EAAeE,KAAK,IAAIH,QAC1DtB,EAAKjE,OACLyF,EAAkB,IAAIA,IAGtBxB,EAAO,IAEXA,GAAQwB,CACZ,CACJ,CAEA,OADAhB,EAAIR,KAAOA,EACJQ,EAAItB,iBAAcwC,CAC7B,CCxVA,MCzIMC,EAAyB,CAC3BzE,OAAQA,OAAO0E,QAAU1E,OAAO2E,QAQ9BC,EAAqB,UAKZ,MAAMC,EAOjB/E,cAAoC,IAAxB,cAAEgF,GAAsB,uDAAJ,CAAC,EAE7BnG,KAAKoG,ODJb,SAAgBC,GACd,IASIC,EATAC,EAAQF,EAAQE,MAChBC,EAAeH,EAAQhF,OACvBoF,EAA0BJ,EAAQI,yBAA2BpF,OAC7DqF,EAAgBL,EAAQK,cACxBC,EAAY,CAAC,EACbC,EAAa,GACbC,EAAe,CAAC,EAChBC,GAAQ,EACRC,EAAc,YAGdC,EAAW,SAASC,GACtB,IAAIC,EACJ,IACEA,EAAOpG,KAAKqG,MAAMF,EAAMC,KAGzB,CAFC,MAAOhE,GACP,MACD,CAED,KAAIwD,GAAiBO,EAAMG,SAAWV,IAIlCQ,GAAQA,EAAKd,QAAUc,EAAKX,QAAUA,EAAO,CAC/C,IAAIc,EAAqBV,EAAUO,EAAKI,QACxC,GAAID,EACF,IAAK,IAAIxF,EAAI,EAAGA,EAAIwF,EAAmBnH,OAAQ2B,IAC7CwF,EAAmBxF,GAAG0F,KAAK,KAAML,EAAKnE,aAGxC8D,EAAaK,EAAKI,QAAUT,EAAaK,EAAKI,SAAW,GACzDT,EAAaK,EAAKI,QAAQtE,KAAKkE,EAAKnE,OAEvC,CACF,EAED0D,EAAwBe,iBAAiB,UAAWR,GAAU,GAE9D,IAAIZ,EAAS,CACXqB,OAAQ,SAAUH,EAAQI,GACxBf,EAAUW,GAAUX,EAAUW,IAAW,GACzCX,EAAUW,GAAQtE,KAAK0E,GAEvB,IAAIC,EAAwBd,EAAaS,GACzC,GAAIK,EAEF,IADA,IAAIN,EAAqBV,EAAUW,GAC1BzF,EAAI,EAAGA,EAAIwF,EAAmBnH,OAAQ2B,IAC7C,IAAK,IAAI+F,EAAI,EAAGA,EAAID,EAAsBzH,OAAQ0H,IAChDP,EAAmBxF,GAAG0F,KAAK,KAAMI,EAAsBC,WAItDf,EAAaS,EACrB,EAEDO,KAAM,SAAUC,GACd,IAAIR,EAASQ,EAAKR,QAEbR,GAASgB,EAAKR,SAAWP,IAAiBP,GAAoD,mBAA7BA,EAAauB,YACjFvB,EAAauB,YAAYjH,KAAKC,UAAU,CACtCqF,QAAQ,EACRG,MAAOA,EACPe,OAAQA,EACRvE,OAAQ+E,EAAK/E,SACX,KAEJ6D,EAAW5D,KAAK8E,EAEnB,EAEDhB,MAAO,SAAUY,GACXZ,EACFY,IAEAM,YAAW,WAAc5B,EAAOU,MAAMY,EAAY,GAAE,GAEvD,EAEDO,QAAS,SAAUP,GACjBQ,cAAc5B,GACdQ,GAAQ,EACJL,GAAkF,mBAAhDA,EAAwB0B,qBAC5D1B,EAAwB0B,oBAAoB,UAAWnB,GAEzDU,GAAYA,GACb,GAGCU,GAAgB,IAAIC,KAASC,KAAKC,SAAW,GA0BjD,OAxBAjC,EAAiBkC,aAAY,WAC3BpC,EAAOyB,KAAK,CACVP,OAAQP,EACRhE,OAAQqF,GAEX,GAAE,IAEHhC,EAAOqB,OAAOV,GAAa,SAAU0B,GACnC,GAAIA,IAAOL,EAAc,CACvBF,cAAc5B,GACdQ,GAAQ,EAER,IAAK,IAAIjF,EAAI,EAAGA,EAAI+E,EAAW1G,OAAQ2B,IACrCuE,EAAOyB,KAAKjB,EAAW/E,IAEzB+E,EAAa,EACd,MACCR,EAAOyB,KAAK,CACVP,OAAQP,EACRhE,OAAQ0F,GAGb,IAEMrC,CACR,CChHqBsC,CAAO,IACd5C,KACAK,IAGPnG,KAAK2I,iBAAmB,OAKxB3I,KAAKoG,OAAOqB,OACRxB,GACA2C,GAAW5I,KAAK2I,iBAAiBC,IACxC,CAODC,UACI7I,KAAKoG,OAAO6B,SACf,CAQDJ,KAAKe,GACD5I,KAAKoG,OAAOyB,KAAK,CACbP,OAAQrB,EACRlD,OAAQ6F,GAEf,CAQDE,mBAAmBpB,GACf1H,KAAK2I,iBAAmBjB,CAC3B,ECtEE,MAOMqB,EAAuB,UAOvBC,EAAwB,WCTtB,MAAMC,EAOjB9H,cAA8B,IAAlB,QAAE+H,GAAgB,uDAAJ,CAAC,EAOvBlJ,KAAKmJ,WAAa,IAAIC,IAQtBpJ,KAAKqJ,WAAa,EAQlBrJ,KAAKsJ,kBAAoB,IAAIF,IAS7BpJ,KAAKuJ,qBAAuB,IAAIC,IAKhCxJ,KAAKyJ,YAAczJ,KAAK0J,GAEpBR,GACAlJ,KAAK2J,WAAWT,EAEvB,CAODU,kBACQ5J,KAAK6J,WACL7J,KAAK6J,SAAShB,UACd7I,KAAK6J,SAAW,KAEvB,CAQDC,mBAAmBlB,GACf,GAAIA,EAAQmB,OAASf,EAAuB,CACxC,MAAMgB,EAAUhK,KAAKsJ,kBAAkBW,IAAIrB,EAAQH,IAE/CuB,IACAA,EAAQpB,GACR5I,KAAKsJ,kBAAkBY,OAAOtB,EAAQH,IAE7C,MAAUG,EAAQmB,OAAShB,EACxB/I,KAAK2B,KAAK,UAAWiH,EAAQ1B,MAAM,CAACiD,EAAQC,KACxCpK,KAAK6J,SAAShC,KAAK,CACfkC,KAAMf,EACNoB,QACA3B,GAAIG,EAAQH,GACZ0B,UAJJ,IAQJnK,KAAK2B,KAAK,QAASiH,EAAQ1B,KAElC,CAOD2B,UACI7I,KAAKsJ,kBAAkBvJ,QACvBC,KAAKuJ,qBAAqBxJ,QAC1BC,KAAKqK,qBACLrK,KAAK4J,iBACR,CAUDjI,KAAK2I,GAAoB,2BAANC,EAAM,iCAANA,EAAM,kBACrB,MAAMC,EAAoBxK,KAAKmJ,WAAWc,IAAIK,GAC9C,IAAIG,GAAc,EAYlB,OAVID,GAAqBA,EAAkBE,MACvCF,EAAkBvJ,SAAQ+F,IACtByD,EAAczD,KAAYuD,IAASE,CAAnC,IAIHA,GACDzK,KAAKuJ,qBAAqBoB,IAAIJ,GAG3BE,CACV,CAWDf,GAAGY,EAAWtD,GACV,IAAIwD,EAAoBxK,KAAKmJ,WAAWc,IAAIK,GAe5C,OAbKE,IACDA,EAAoB,IAAIhB,IACxBxJ,KAAKmJ,WAAW9D,IAAIiF,EAAWE,IAGnCA,EAAkBG,IAAI3D,GAEtBhH,KAAKuJ,qBAAqBtI,SAAQsJ,IAC1BvD,KAAYuD,IACZvK,KAAKuJ,qBAAqBW,OAAOK,EACpC,IAGEvK,IACV,CAUDqK,mBAAmBC,GAOf,OANIA,EACAtK,KAAKmJ,WAAWe,OAAOI,GAEvBtK,KAAKmJ,WAAWpJ,QAGbC,IACV,CAWD4K,eAAeN,EAAWtD,GACtB,MAAMwD,EAAoBxK,KAAKmJ,WAAWc,IAAIK,GAM9C,OAJIE,GACAA,EAAkBN,OAAOlD,GAGtBhH,IACV,CAQD6K,YAAsB,IAAZ5D,EAAY,uDAAJ,CAAC,EACXjH,KAAK6J,UACL7J,KAAK6J,SAAShC,KAAK,CACfkC,KD/MkB,QCgNlB7C,KAAMD,GAGjB,CAQD6D,YAAYC,GACR,IAAK/K,KAAK6J,SACN,OAAOmB,QAAQC,OAAO,IAAIC,MAAM,kCAGpClL,KAAKqJ,aAEL,MAAMZ,EAAKzI,KAAKqJ,WAEhB,OAAO,IAAI2B,SAAQ,CAACG,EAASF,KACzBjL,KAAKsJ,kBAAkBjE,IAAIoD,GAAI,IAAuB,IAAtB,MAAE2B,EAAF,OAASD,GAAa,OAC5B,IAAXA,EACPgB,EAAQhB,GAIRc,OADwB,IAAVb,EACPA,EAEA,IAAIc,MAAM,+BACpB,IAGLlL,KAAK6J,SAAShC,KAAK,CACfkC,KAAMhB,EACN7B,KAAM6D,EACNtC,MAHJ,GAMP,CAQDkB,WAAWT,GACPlJ,KAAK4J,kBAEL5J,KAAK6J,SAAWX,EAChBlJ,KAAK6J,SAASf,mBAAmB9I,KAAK8J,mBAAmBsB,KAAKpL,MACjE,GLrPE,SAAwB2E,EAAK0G,GAAY,EAAOC,EAAS,QACzC,iBAAR3G,IAEPA,EAAM,IAAID,IAAIC,IAElB,MAAM4G,EAAsB,WAAXD,EAAsB3G,EAAIT,OAASS,EAAIR,KAClDpB,EAAS,CAAC,EACVyI,EAAaD,GAAUE,OAAO,GAAGC,MAAM,MAAQ,GAErD,GAAe,SAAXJ,GAA2C,IAAtBE,EAAWtL,OAAc,CAC9C,MAAMyL,EAAaH,EAAW,GAC9B,GAAIG,EAAW/I,WAAW,MAAyC,IAAjC+I,EAAWD,MAAM,KAAKxL,OACpD,OAAO6C,CAEf,CAqBA,OApBAyI,EAAWvK,SAAS2K,IAChB,MAAMC,EAAQD,EAAKF,MAAM,KACnBhL,EAAMmL,EAAM,GAClB,IAAKnL,GAAOA,EAAIgL,MAAM,KAAKI,MAAMC,GAAM/J,EAAUD,SAASgK,KACtD,OAEJ,IAAIC,EACJ,IAEI,GADAA,EAAQH,EAAM,IACTR,EAAW,CACZ,MAAMY,EAAUC,mBAAmBF,GAAOxI,QAAQ,MAAO,KACzDwI,EAAoB,cAAZC,OAA0BpG,EAAY,QAAaoG,EAC/D,CAKJ,CAHA,MAAO/I,GAEH,YMmCL,SAAqBA,EAAGiJ,EAAM,IACjC3K,QAAQ4I,MAAM+B,EAAKjJ,GACnB7B,OAAO+K,UAAUD,OAAKtG,OAAWA,OAAWA,EAAW3C,EAC3D,CNvCYmJ,CAAYnJ,EAAG,wCAAwCoJ,OAAON,KAElE,CACAjJ,EAAOrC,GAAOsL,CAAK,IAEhBjJ,CACX,EO7CsBwJ,CAAelL,OAAOmL,UAAUC,4BD4D7CpL,OAAOqL,cACRrL,OAAOqL,YAAc,CAAC,GAErBrL,OAAOqL,YAAYC,MACpBtL,OAAOqL,YAAYC,IAAM,CAAC,GAEvBtL,OAAOqL,YAAYC,KE3BPC,4BAA8BC,GArBjDC,UAsBUnD,WAAWkD,G,aCjDzB,MAAME,E,MAASC,GAAAA,U,qCAgIf,SAASC,EAAWH,EAAWI,GAC3B,OAAOJ,EAAUhC,YAAY,CACzBf,KAAM,UACNoD,KAAM,YACND,UAEP,CCpHD,MAAME,EAA0B,CAC5B,cAAe,2BAObC,EAAW,CACbC,gBAAiB,oBACjBC,0BAA2B,8BAC3BC,aAAc,gBACdC,YAAa,gBACbC,0BAA2B,gCAC3BC,UAAW,aACXC,kBAAmB,sBACnBC,kBAAmB,sBACnBC,YAAa,eACbC,cAAe,iBACfC,MAAO,QACPC,eAAgB,kBAChBC,OAAQ,eACRC,iBAAkB,oBAClBC,oBAAqB,wBACrBC,iBAAkB,qBAClBC,aAAc,gBACdC,gBAAiB,mBACjBC,aAAc,gBACdC,gBAAiB,mBACjBC,eAAgB,kBAChBC,SAAU,WACVC,eAAgB,kBAChBC,kBAAmB,qBACnBC,mBAAoB,uBACpBC,gBAAiB,oBACjBC,iBAAkB,qBAClBC,gBAAiB,oBACjBC,wBAAyB,6BACzBC,sBAAuB,2BACvBC,UAAW,aACXC,uBAAwB,4BACxBC,YAAa,gBACbC,yBAA0B,8BAC1BC,sBAAuB,2BACvBC,2BAA4B,gCAC5BC,qBAAsB,yBACtBC,aAAc,gBACdC,YAAa,gBACbC,gBAAiB,oBACjBC,iBAAkB,oBAClBC,eAAgB,kBAChBC,gBAAiB,oBACjBC,cAAe,iBACfC,eAAgB,mBAChBC,QAAS,UACTC,eAAgB,kBAChBC,YAAa,eACbC,aAAc,gBACdC,mBAAoB,uBACpBC,WAAY,cACZC,WAAY,cACZC,gBAAiB,oBACjBC,YAAa,eACbC,iBAAkB,oBAClBC,uBAAwB,2BACxBC,uBAAwB,2BACxBC,gBAAiB,oBACjBC,kBAAmB,sBACnBC,gBAAiB,mBACjBC,eAAgB,mBAChBC,8BAA+B,4BAC/BC,YAAa,eACbC,iBAAkB,qBAOhBC,EAAS,CACX,iBAAkB,gBAClB,6BAA8B,2BAC9B,4BAA6B,yBAC7B,iCAAkC,6BAClC,yBAA0B,uBAC1B,kBAAmB,iBACnB,eAAgB,cAChB,eAAgB,cAChB,uCAAwC,oCACxC,sBAAuB,oBACvB,sBAAuB,oBACvB,sBAAuB,oBACvB,sBAAuB,oBACvB,eAAgB,cAChB,iBAAkB,gBAClB,iCAAkC,8BAClC,yBAA0B,uBAC1B,qBAAsB,oBACtB,4BAA6B,0BAC7B,4BAA6B,0BAC7B,mBAAoB,kBACpB,uBAAwB,sBACxB,IAAO,MACP,YAAa,WACb,kCAAmC,gCACnC,kCAAmC,gCACnC,4BAA6B,0BAC7B,cAAe,aACf,cAAe,aACf,aAAc,YACd,yBAA0B,wBAC1B,mBAAoB,kBACpB,qBAAsB,mBACtB,qBAAsB,oBACtB,yBAA0B,uBAC1B,mBAAoB,kBACpB,2BAA4B,yBAC5B,4BAA6B,0BAC7B,oBAAqB,mBACrB,0BAA2B,wBAC3B,wBAAyB,sBACzB,yBAA0B,uBAC1B,qBAAsB,mBACtB,2BAA4B,yBAC5B,2BAA4B,yBAC5B,kCAAmC,6BACnC,uBAAwB,eACxB,0BAA2B,wBAC3B,wBAAyB,sBACzB,6BAA8B,2BAC9B,4BAA6B,yBAC7B,wBAAyB,sBACzB,gCAAiC,6BACjC,2BAA4B,yBAC5B,iBAAkB,gBAClB,mBAAoB,kBACpB,oBAAqB,kBACrB,yBAA0B,uBAC1B,4BAA6B,2BAQjC,IAAI7I,EAAK,EAWT,SAAS8I,EAAwBC,EAAaC,GAC1CD,EAAYE,uBAAyBD,CACxC,CAsFD,SAASE,EAAe3F,GACpB,IAAI4F,EAaJ,MANqB,iBAAV5F,GAAkD,OAA5BM,OAAON,GAAO1J,MAFpC,kCAGPsP,EAAc5F,EACU,iBAAVA,IACd4F,EAAe,GAAE5F,OAGd4F,CACV,CAMc,MAAMC,UAA6B/R,KAkC9CqB,YAAY2D,GACR1D,QADyB,2BAANmJ,EAAM,iCAANA,EAAM,kBAEzB,MAAM,SACFvF,EAAW,GADT,MAEF8M,EAAQ,OAFN,OAGFC,EAAS,OAHP,WAIFC,EAAaC,SAASC,KAJpB,gBAKF3M,EAAkB,CAAC,EALjB,yBAMF4M,EAA2B,CAAC,EAN1B,IAOFlN,EAPE,KAQFC,EARE,OASFkN,EATE,SAUFC,EAVE,QAWFC,EAXE,SAYFC,EAZE,QAaFC,EAbE,QAcFrN,EAdE,QAeFsN,EAAU,IA9HtB,SAAwBlI,GACpB,IAAKA,EAAKrK,OACN,MAAO,CAAC,EAKZ,cAFiBqK,EAAK,IAGtB,IAAK,SACL,IAAK,YAAa,CAId,MACIvF,EACA8M,EACAC,EACAC,EACAzM,EACA4M,EACAlN,EACAmN,EACAlN,GACAqF,EAEJ,MAAO,CACHvF,WACA8M,QACAC,SACAC,aACAzM,kBACA4M,2BACAlN,MACAmN,SACAlN,OAEP,CACD,IAAK,SACD,OAAOqF,EAAK,GAChB,QACI,MAAM,IAAIW,MAAM,8BAEvB,CAqFWwH,CAAenI,GACbzI,EAAsBZ,EAAkBb,QAAQ,qBAEtDL,KAAK2S,YAAcX,EACnBhS,KAAK4S,KAjJb,SAAqB9N,GACjB,OAAOT,EAAkB,IADc,uDAAJ,CAAC,EAGhCM,IAAM,WAAUG,iCAAsC2D,KAE7D,CA4ImBoK,CAAY/N,EAAQ,CAC5BS,kBACA4M,2BACAlN,MACAC,OACAF,WACAsN,UACAC,WACAO,QAAS,CACLhR,uBAEJqD,YAEJnF,KAAK+S,cAAchB,EAAQD,EAAOM,EAAQK,GAC1CzS,KAAKgT,WAAa,IAAI/J,EAAU,CAC5BC,QAAS,IAAIhD,EAA4B,CACrCC,cAAe,CACXO,cAAe,IAAIhC,IAAI1E,KAAK4S,MAAMxL,OAClCb,MAAQ,2BAA0BkC,IAClCpH,OAAQrB,KAAKiT,OAAOC,mBAI5BC,MAAMC,QAAQf,IAAaA,EAASnS,OAAS,GAC7CF,KAAKqT,OAAOhB,GAEhBrS,KAAKsT,YAAcd,EACnBxS,KAAKuT,sBAAuB,EAC5BvT,KAAKwT,wBAAyB,EAC9BxT,KAAK0R,sBAAwB,EAC7B1R,KAAKyT,cAAgB,CAAC,EACtBzT,KAAK0T,eAAY7N,EACjB7F,KAAK2T,yBAAsB9N,EAC3B7F,KAAK4T,kBACLnL,GACH,CAgBDsK,cAAchB,EAAQD,EAAOM,EAAQK,GACjC,MAAMoB,EAAa,uBAAsBpL,IAEzCzI,KAAKiT,OAAShB,SAAS6B,cAAc,UACrC9T,KAAKiT,OAAOc,MAAQ,sEACpB/T,KAAKiT,OAAO9F,KAAO0G,EACnB7T,KAAKiT,OAAOxK,GAAKoL,EACjB7T,KAAKgU,SAASjC,EAAQD,GACtB9R,KAAKiT,OAAOgB,aAAa,kBAAmB,QAC5CjU,KAAKiT,OAAOiB,MAAMC,OAAS,EAEvB1B,IACAzS,KAAKiT,OAAOR,QAAUA,GAGtBL,IAGApS,KAAKiT,OAAOb,OAASA,GAEzBpS,KAAKiT,OAAOmB,IAAMpU,KAAK4S,KAEvB5S,KAAKiT,OAASjT,KAAK2S,YAAY0B,YAAYrU,KAAKiT,OACnD,CAODqB,2BACI,MAAMC,EAAevU,KAAKiT,OAAOC,cAC3BsB,EAAiBD,EAAatC,SACpC,IAAIwC,EAAU,GACd,MAAMC,EAAOF,EAAeG,cAAc,QAE1C,GAAID,GAAQA,EAAKE,KACbH,EAAUC,EAAKE,SACZ,CACH,MAAM,SAAEpS,EAAF,KAAYoB,GAAS2Q,EAAa/H,SAExCiI,EAAW,GAAEjS,MAAaoB,GAC7B,CAED,OAAOwJ,EAAwByH,KAC3BC,GAAY,IAAIpQ,IAAIoQ,EAAUL,GAASG,MAE9C,CAQDG,yBAAyBC,GACrB,MAAM,qBAAEC,GACFjV,KAAKyT,cAAcuB,IAAkB,CAAC,EAE5C,OAAOC,CACV,CAODC,yBACI,OAAOlV,KAAK2T,mBACf,CAQDwB,iBACI,MAAMC,EAASpV,KAAKqV,YAEpB,GAAKrV,KAAKuT,sBACE6B,GACAA,EAAOlC,eACPkC,EAAOlC,cAAcjB,SAIjC,OAAOmD,EAAOlC,cAAcjB,SAASqD,eAAe,aACvD,CAODC,mBACI,MAAMH,EAASpV,KAAKqV,YAEpB,GAAKrV,KAAKwT,wBACE4B,GACAA,EAAOlC,eACPkC,EAAOlC,cAAcjB,SAIjC,OAAOmD,EAAOlC,cAAcjB,SAASqD,eAAe,eACvD,CAUDE,qBAAqBR,GACjB,MAAMI,EAASpV,KAAKqV,YAEpB,GAAKD,GACOA,EAAOlC,eACPkC,EAAOlC,cAAcjB,SAIjC,YAA6B,IAAlB+C,GAAiCA,IAAkBhV,KAAK0T,UACxD0B,EAAOlC,cAAcjB,SAASqD,eAAe,wBAGjDF,EAAOlC,cAAcjB,SAAS0C,cAAe,gBAAeK,UACtE,CAWDhB,SAASjC,EAAQD,GACb,MAAM2D,EAAe9D,EAAeI,GAC9B2D,EAAc/D,EAAeG,QAEdjM,IAAjB4P,IACAzV,KAAK2V,QAAU5D,EACf/R,KAAKiT,OAAOiB,MAAMnC,OAAS0D,QAGX5P,IAAhB6P,IACA1V,KAAK4V,OAAS9D,EACd9R,KAAKiT,OAAOiB,MAAMpC,MAAQ4D,EAEjC,CASD9B,kBACI5T,KAAKgT,WAAWtJ,GAAG,SAAS,IAAuB,IAAtB,KAAEyD,KAASjG,GAAW,EAC/C,MAAM2O,EAAS3O,EAAKuB,GAEpB,OAAQ0E,GACR,IAAK,0BACD,QAAgC,IAArBnN,KAAKsT,YAA6B,CAEzC,MAAMwC,EAAaC,IACf,MAAMC,EAAQ,GAEd,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAI7V,OAAQ+V,GAAK,EACjCD,EAAMhT,KAAKkT,SAASH,EAAIrT,UAAUuT,EAAGA,EAAI,GAAI,KAGjD,OAAOD,CAAP,EAGJhW,KAAKmW,eAAe,wBAAyBrV,KAAKC,UAAU,CACxDqV,YAAaN,EAAW9V,KAAKsT,aAC7B+C,MAAO,KAGXrW,KAAKsT,iBAAczN,CACtB,CAED7F,KAAK0T,UAAYmC,EACjB7V,KAAKyT,cAAcoC,GAAU,CACzB7H,MAAO9G,EAAK8G,MACZsI,UAAWpP,EAAKoP,WAKxB,IAAK,qBACDtW,KAAKyT,cAAcoC,GAAU7V,KAAKyT,cAAcoC,IAAW,CAAC,EAC5D7V,KAAKyT,cAAcoC,GAAQ/H,YAAc5G,EAAK4G,YAC9C9N,KAAKyT,cAAcoC,GAAQZ,qBACrB/N,EAAK+N,qBACX1D,EAAwBvR,KAAM,GAC9B,MAEJ,IAAK,mBACDuR,EAAwBvR,MAAO,UACxBA,KAAKyT,cAAcoC,GAC1B,MACJ,IAAK,sBAAuB,CACxB,MAAMU,EAAOvW,KAAKyT,cAAcoC,GAE5BU,IACAA,EAAKzI,YAAc5G,EAAKsP,YACxBD,EAAKtB,qBAAuB/N,EAAK+N,sBAErC,KACH,CACD,IAAK,eAAgB,CACjB,MAAMsB,EAAOvW,KAAKyT,cAAcoC,GAE5BU,IACAA,EAAKvI,MAAQ9G,EAAK8G,OAEtB,KACH,CACD,IAAK,iBAAkB,CACnB,MAAMuI,EAAOvW,KAAKyT,cAAcoC,GAE5BU,IACAA,EAAKD,UAAYpP,EAAKoP,WAE1B,KACH,CACD,IAAK,+BACDtW,KAAK2T,oBAAsBkC,EAC3B7V,KAAK2B,KAAK,qBACV,MACJ,IAAK,iCACD3B,KAAKuT,qBAAuBrM,EAAKuP,UACjCzW,KAAK2B,KAAK,qBACV,MACJ,IAAK,wBACD3B,KAAKyT,cAAcoC,GAAU,CACzB/H,YAAa5G,EAAK4G,YAClBmH,qBAAsB/N,EAAK+N,sBAE/B,MACJ,IAAK,2BACDjV,KAAKwT,uBAAyBtM,EAAKuP,UACnCzW,KAAK2B,KAAK,uBACV,MACJ,IAAK,wBACD4P,EAAwBvR,MAAO,UACxBA,KAAKyT,cAAczT,KAAK0T,WAC/B,MACJ,IAAK,wBACD1T,KAAK0W,cAAgBxP,EAAKyP,aAC1B,MACJ,IAAK,yBACD3W,KAAK4W,2BAA2B1P,EAAK2P,OACrC,MACJ,IAAK,wBAID,OAHA3V,EAAkBX,QAAQ,oBAAqB2G,EAAKpF,sBAG7C,EAGX,MAAMwI,EAAYgH,EAAOnE,GAEzB,QAAI7C,IACAtK,KAAK2B,KAAK2I,EAAWpD,IAEd,EAGX,GAEP,CAQD0P,2BAA2BC,GACvB,IAAKA,IAAU1W,OAAOC,KAAKyW,GAAO3W,OAC9B,OAGJ,MAAM4W,EAAkB3W,OAAOC,KAAKyW,GAAOE,QAAO,CAACC,EAAMC,KAAgB,MACrE,iBAAIJ,EAAMI,UAAV,OAAI,EAAoBC,aACb/W,OAAOC,KAAKyW,EAAMI,GAAaC,cAAchX,OAAS8W,EAG1DA,CAAP,GACD,GAEHhX,KAAK0R,sBAAwBoF,CAChC,CAOiB,qBACd,OAAO9W,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,cAEb,CAODgK,cACI,OAAOnX,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,kBAEb,CAYD3F,iBAAiBP,EAAOD,GACpBhH,KAAK0J,GAAGzC,EAAOD,EAClB,CAqFDoQ,kBAAkBzQ,GACd,IAAK,MAAMM,KAASN,EAChB3G,KAAKwH,iBAAiBP,EAAON,EAAUM,GAE9C,CAQDoQ,8BACI,OAAOrX,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,iCAEb,CAODtE,UACI7I,KAAK2B,KAAK,gBACV3B,KAAKgT,WAAWnK,UAChB7I,KAAKqK,qBACDrK,KAAKiT,QAAUjT,KAAKiT,OAAOjB,YAC3BhS,KAAKiT,OAAOjB,WAAWsF,YAAYtX,KAAKiT,OAE/C,CAmBDkD,eAAehJ,GACX,GAAMA,KAAQE,EAAd,CAD0B,2BAAN9C,EAAM,iCAANA,EAAM,kBAM1BvK,KAAKgT,WAAWnI,UAAU,CACtB3D,KAAMqD,EACN4C,KAAME,EAASF,IAHlB,MAHG3L,QAAQ4I,MAAM,8BAQrB,CAiBDmN,gBAAgBC,GACZ,IAAK,MAAM9W,KAAO8W,EACdxX,KAAKmW,eAAezV,EAAK8W,EAAY9W,GAE5C,CAOD+W,sBACI,OD53BD,SAA6B3K,GAChC,OAAOA,EAAUhC,YAAY,CACzBf,KAAM,UACNoD,KAAM,wBACPuK,OAAMxU,IACL6J,EAAO3C,MAAMlH,GAEN,CAAC,IAEf,CCm3BcuU,CAAoBzX,KAAKgT,WACnC,CAOD2E,gCACI,OAAO3X,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,oCAEb,CAODyK,oBACI,OD93BD,SAA2B9K,GAC9B,OAAOA,EAAUhC,YAAY,CACzBf,KAAM,UACNoD,KAAM,sBACPuK,OAAMxU,IACL6J,EAAO3C,MAAMlH,GAEN,CAAC,IAEf,CCq3Bc0U,CAAkB5X,KAAKgT,WACjC,CAOD6E,6BACI,OAAO7X,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,iCAEb,CAQD2K,mBACI,OAAO9X,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,sBAEb,CAQD4K,sBACI,MAAMC,EAAiB7X,OAAOC,KAAKJ,KAAKyT,eAClCwE,EAAmB9X,OAAO+X,OAAOlY,KAAKyT,eAM5C,OAJAwE,EAAiBhX,SAAQ,CAACkX,EAAaC,KACnCD,EAAYnD,cAAgBgD,EAAeI,EAA3C,IAGGH,CACV,CAODI,kBACI,OAAOrY,KAAK0W,aACf,CAQD4B,mBACI,OAAOtY,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,sBAEb,CAUDoL,wBAAwBC,GACpB,ODl7BD,SAAiC1L,EAAW0L,GAC/C,OAAO1L,EAAUhC,YAAY,CACzB0N,aACAzO,KAAM,UACNoD,KAAM,2BAEb,CC46BcoL,CAAwBvY,KAAKgT,WAAYwF,EACnD,CAQDC,wBACI,OD56BD,SAA+B3L,GAClC,OAAOA,EAAUhC,YAAY,CACzBf,KAAM,UACNoD,KAAM,yBAEb,CCu6BcsL,CAAsBzY,KAAKgT,WACrC,CAQD0F,gCACI,ODv6BD,SAAuC5L,GAC1C,OAAOA,EAAUhC,YAAY,CACzBf,KAAM,UACNoD,KAAM,iCAEb,CCk6BcuL,CAA8B1Y,KAAKgT,WAC7C,CAQDK,OAAOhB,GACH,OAAKc,MAAMC,QAAQf,IAAiC,IAApBA,EAASnS,OAIlCF,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,SACNkF,aALOrH,QAAQC,OAAO,IAAI0N,UAAU,oBAO3C,CAQDC,eACI,OAAO5Y,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,kBAEb,CAQD0L,kBACI,OAAO7Y,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,qBAEb,CASD2L,eAAeC,GACX,OAAO/Y,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,mBACN4L,aAEP,CAUDC,wBAAwBhE,EAAe+D,GACnC,OAAO/Y,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,6BACN6H,gBACA+D,aAEP,CAQDE,yBACI,OAAOjZ,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,6BAEb,CAOD+L,kBACI,OAAOlZ,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,qBAEb,CAODgM,gBACI,OAAOnZ,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,mBAEb,CAQDiM,aAAapE,GACT,MAAM,UAAEsB,GAActW,KAAKyT,cAAcuB,IAAkB,CAAC,EAE5D,OAAOsB,CACV,CAOD+C,oBACI,OAAOrZ,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,mBAEb,CAQDmM,eAAetE,GACX,MAAM,YAAElH,GAAgB9N,KAAKyT,cAAcuB,IAAkB,CAAC,EAE9D,OAAOlH,CACV,CAQDyL,SAASvE,GACL,MAAM,MAAEhH,GAAUhO,KAAKyT,cAAcuB,IAAkB,CAAC,EAExD,OAAOhH,CACV,CAODqH,YACI,OAAOrV,KAAKiT,MACf,CAQDuG,0BACI,OAAOxZ,KAAK0R,qBACf,CAQD+H,mBACI,OAAOzZ,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,sBAEb,CAQDuM,eACI,OAAO1Z,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,kBAEb,CAODwM,oBACI,OAAO3Z,KAAKgT,WAAWlI,YAAY,CAC/BqC,KAAM,uBAEb,CAYDyB,eAAeoG,EAAe4E,GAC1B5Z,KAAKmW,eAAe,iBAAkBnB,EAAe4E,EACxD,CAWDzR,oBAAoBlB,GAChBjH,KAAKqK,mBAAmBpD,EAC3B,CAWD4S,qBAAqBC,GACjBA,EAAU7Y,SAAQgG,GAASjH,KAAKmI,oBAAoBlB,IACvD,CASD+H,iBAAiB8C,EAAOC,GAChBD,GAAS9R,KAAK4V,QAAU7D,GAAU/R,KAAK2V,SACvC3V,KAAKmW,eAAe,mBAAoBrE,EAAOC,EAEtD,CAcDgI,yBAAyB9S,GACrBjH,KAAKgT,WAAWnI,UAAU,CACtB3D,KAAM,CAAED,GACRkG,KAAM,0BAEb,CAUD6M,oBAAoBC,EAAOC,GACvB,ODzrCD,SAA6BpN,EAAWmN,EAAOxR,GAClD,OAAOwE,EAAWH,EAAW,CACzBrE,KACA0R,KAAM,aACNF,SAEP,CCmrCcD,CAAoBha,KAAKgT,WAAYiH,EAAOC,EACtD,CAUDE,qBAAqBH,EAAOC,GACxB,ODprCD,SAA8BpN,EAAWmN,EAAOxR,GACnD,OAAOwE,EAAWH,EAAW,CACzBrE,KACA0R,KAAM,cACNF,SAEP,CC8qCcG,CAAqBpa,KAAKgT,WAAYiH,EAAOC,EACvD,CAaD3K,yBAAyByF,EAAe4E,GACpC5Z,KAAKmW,eAAe,2BAA4BnB,EAAe4E,EAClE,CAUDS,oBAAoBJ,EAAOC,GACvB,OD9qCD,SAA6BpN,EAAWmN,EAAOxR,GAClD,OAAOwE,EAAWH,EAAW,CACzBrE,KACA0R,KAAM,aACNF,SAEP,CCwqCcI,CAAoBra,KAAKgT,WAAYiH,EAAOC,EACtD,CAqBDnK,eAAe1J,GACXrG,KAAKmW,eAAe,iBAAkB9P,EACzC,CAQD4J,cAAcqK,GACVta,KAAKmW,eAAe,gBAAiBmE,EACxC,CAQD7J,WAAW8J,GACPva,KAAKmW,eAAe,aAAcoE,EACrC,CAU0B,4BAACC,GACxB,MAAM,IAAE9Z,EAAF,MAAO2V,GAAUmE,EAEvB,GAAI9Z,EAAK,CACL,MAAM0V,QAAoBqE,OAAOC,OAAOC,UAAU,MAAOja,GAEzDV,KAAKmW,eAAe,wBAAyBrV,KAAKC,UAAU,CACxDqV,YAAajD,MAAMyH,KAAK,IAAIC,WAAWzE,IACvCC,UACP,MACGrW,KAAKmW,eAAe,wBAAyBrV,KAAKC,UAAU,CACxDqV,aAAa,EACbC,UAEX,E,gBCn4CL5W,EAAOD,QAAU,EAAjBC,KAAAA,O,2BCCA,MAAMqb,EACS,iJAIftb,EAAQ2H,MAAQ,SAAU4T,GAItB,MAAMC,EAAkC,iBAAnB,oFACfC,GAAU,0CAAc,IAAMD,EAApB,6CAA6CnV,EACvDQ,GAAW,0CAAc,IAAd,0CAA+B2U,GAAgB,CAAC,EAI3DlY,EAAMhC,KAAKqG,MAAM4T,EAAME,GAI7B,MAA4B,WAAxB5U,EAAQ6U,YACDpY,EAKNA,GACc,iBAARA,GAONiY,EAAKzY,MAAMwY,IAMhBtb,EAAQ2b,KAAKrY,EAAKuD,GAEXvD,GAbIA,CAcd,EAGDtD,EAAQ2b,KAAO,SAAUrY,GAAmB,IAAduD,EAAc,uDAAJ,CAAC,EAEjC+U,EAAO,CAACtY,GAEZ,KAAOsY,EAAKlb,QAAQ,CAChB,MAAMmb,EAAQD,EACdA,EAAO,GAEP,IAAK,MAAME,KAAQD,EAAO,CACtB,GAAIlb,OAAOob,UAAUC,eAAejU,KAAK+T,EAAM,aAAc,CACzD,GAA4B,WAAxBjV,EAAQ6U,YACR,MAAM,IAAIO,YAAY,uDAGnBH,EAAKI,SACf,CAED,IAAK,MAAMhb,KAAO4a,EAAM,CACpB,MAAMtP,EAAQsP,EAAK5a,GACfsL,GACiB,iBAAVA,GAEPoP,EAAKpY,KAAKsY,EAAK5a,GAEtB,CACJ,CACJ,CACJ,EAGDlB,EAAQmc,UAAY,SAAUZ,EAAME,GAEhC,IACI,OAAOzb,EAAQ2H,MAAM4T,EAAME,EAI9B,CAFD,MAAOW,GACH,OAAO,IACV,CACJ,C,gBCxED,IAAI5O,EAAS6O,EAAQ,GAgDrB,SAASC,EAAaC,EAAY1V,GAC9BrG,KAAK+b,WAAaA,EAClB/b,KAAKgc,oBAAmB3V,IAAWA,EAAQ2V,mBAAmB3V,EAAQ2V,iBACtEhc,KAAKic,cAAgB5V,GAAWA,EAAQ4V,cAAgB5V,EAAQ4V,cAAe,IAC/Ejc,KAAKkc,eAAiB7V,GAAWA,EAAQ6V,eAAiB7V,EAAQ6V,eAAiB,IAGnF/b,OAAOC,KAAK4M,EAAOmP,QAAQlb,QAC3B,SAAUmb,GAENpc,KADiBgN,EAAOmP,OAAOC,IACZ,WACfpc,KAAKqc,KAAKC,MAAMtc,KAAMuc,UACzB,EAACnR,KAAKpL,KAAMoc,EAChB,EAAChR,KAAKpL,OAMPA,KAAKwc,oBAAsB,KAM3Bxc,KAAKyc,MAAQ,GAKbzc,KAAK0c,SAAW,EAMhB1c,KAAK2c,YAAc,EACtB,CAgBDb,EAAaP,UAAUxa,UAAY,SAAU6b,GACzC,IACI,OAAO9b,KAAKC,UAAU6b,EAGzB,CAFC,MAAOxS,GACL,MAAO,8BACV,CACJ,EAiBD0R,EAAaP,UAAUsB,iBAAmB,SAC1CT,GAEI,IADA,IAAIjQ,EAAM,GACDtK,EAAI,EAAGib,EAAMP,UAAUrc,OAAQ2B,EAAIib,EAAKjb,IAAK,CAClD,IAAIkb,EAAMR,UAAU1a,IAEf7B,KAAKgc,kBAAoBI,IAAapP,EAAOmP,OAAOa,OACtC,iBAARD,IACPA,EAAM/c,KAAKe,UAAUgc,IAEzB5Q,GAAO4Q,EACHlb,IAAMib,EAAM,IACZ3Q,GAAO,IAEd,CACD,OAAOA,EAAIjM,OAASiM,EAAM,IAC7B,EAQD2P,EAAaP,UAAUc,KAAO,WAG1B,IAAIY,EAAYV,UAAU,GACtBpQ,EAAMnM,KAAK6c,iBAAiBP,MAAMtc,KAAMuc,WAC5C,GAAIpQ,EAAK,CAEL,IAAI+Q,EAAcld,KAAKyc,MAAMzc,KAAKyc,MAAMvc,OAAS,GAC7Cid,EAAkBD,GAAeA,EAAYnC,KAC7CoC,IAAoBhR,EACpB+Q,EAAYE,OAAS,GAErBpd,KAAKyc,MAAMzZ,KAAK,CACZ+X,KAAM5O,EACN8Q,UAAWA,EACXG,MAAO,IAEXpd,KAAK0c,UAAYvQ,EAAIjM,OAE5B,CAEGF,KAAK0c,UAAY1c,KAAKkc,gBACtBlc,KAAKqd,QAAO,GAAkB,EAErC,EAMDvB,EAAaP,UAAU+B,MAAQ,WAC3Btd,KAAKud,4BACR,EAODzB,EAAaP,UAAUgC,2BAA6B,WAC5Cvd,KAAKwc,sBACLnb,OAAOmc,aAAaxd,KAAKwc,qBACzBxc,KAAKwc,oBAAsB,MAG/Bxc,KAAKwc,oBAAsBnb,OAAO2G,WAC9BhI,KAAKqd,OAAOjS,KACRpL,MAAM,GAA0B,GACpCA,KAAKic,cACZ,EAMDH,EAAaP,UAAUkC,MAAQ,WAC3Bzd,KAAKqd,QACD,GACA,EACP,EAaDvB,EAAaP,UAAU8B,OAAS,SAASK,EAAOC,GAExC3d,KAAK0c,SAAW,IAAM1c,KAAK+b,WAAW6B,WAAaF,KAG/C1d,KAAK+b,WAAW6B,WAEZ5d,KAAK2c,YAAYzc,SACjBF,KAAK2c,YAAY1b,QACb,SAAU4c,GACN7d,KAAK+b,WAAW+B,UAAUD,EAC7B,EAACzS,KAAKpL,OAGXA,KAAK2c,YAAc,IAGvB3c,KAAK+b,WAAW+B,UAAU9d,KAAKyc,QAE/Bzc,KAAK2c,YAAY3Z,KAAKhD,KAAKyc,OAG/Bzc,KAAKyc,MAAQ,GACbzc,KAAK0c,SAAW,GAGhBiB,GACA3d,KAAKud,4BAEZ,EAMDzB,EAAaP,UAAUwC,KAAO,WAE1B/d,KAAKqd,QAAO,GAA0B,EACzC,EAED5d,EAAOD,QAAUsc,C,QC/PjB,IAAIK,EAAS,CACT,MAAS,EACT,MAAS,EACT,KAAQ,EACR,IAAO,EACP,KAAQ,EACR,MAAS,GAObnP,EAAOgR,iBAAmBxc,QAM1B,IAAIyc,EAAmB,CAAEjR,EAAOgR,kBAOhChR,EAAOkR,mBAAqB,SAASpR,IACY,IAAzCmR,EAAiBta,QAAQmJ,IACzBmR,EAAiBjb,KAAK8J,EAE7B,EAODE,EAAOmR,sBAAwB,SAASrR,GACpC,IAAIsR,EAAeH,EAAiBta,QAAQmJ,IACtB,IAAlBsR,GACAH,EAAiBI,OAAOD,EAAc,EAE7C,EAKD,IAAIE,EAAgB,CAAC,EAgBrB,SAASC,IACL,IAAIC,EAAa,CACbC,WAAY,GACZC,aAAc,GACdC,KAAM,KACNC,OAAQ,MAGRxU,EAAQ,IAAIc,MACZ2T,EAAQzU,EAAMyU,MAAOzU,EAAMyU,MAAMnT,MAAM,MAAQ,GACnD,IAAImT,GAASA,EAAM3e,OAAS,EACxB,OAAOse,EAEX,IAAIM,EAAI,KAIR,OAHGD,EAAM,KACLC,EAAID,EAAM,GAAGvc,MAAM,iDAEnBwc,GAAKA,EAAE5e,QAAU,GAEe,IAA7B2e,EAAM,GAAGlb,QAAQ,QAEhB6a,EAAWC,WAAaI,EAAM,GAAGpT,OAAO,EAAGoT,EAAM,GAAGlb,QAAQ,MAG5D6a,EAAWC,WAAaI,EAAM,GAAGpT,OAAO,EAAGoT,EAAM,GAAGlb,QAAQ,MAEzD6a,IAGXA,EAAWC,WAAaK,EAAE,GAC1BN,EAAWE,aAAeI,EAAE,GAC5BN,EAAWG,KAAOG,EAAE,GACpBN,EAAWI,OAASE,EAAE,GACfN,EACV,CAQD,SAASO,IACL,IAAIhS,EAASwP,UAAU,GAAIyC,EAAQzC,UAAU,GACzChS,EAAO4I,MAAMoI,UAAU0D,MAAM1X,KAAKgV,UAAW,GACjD,KAAGJ,EAAO6C,GAASjS,EAAOiS,OAQ1B,IAJA,IAAIR,IACIzR,EAAO1G,QAAQ6Y,mBAAqBZ,EAAcY,oBAClDX,IACJY,EAAalB,EAAiBmB,OAAOrS,EAAOoS,YACxCtd,EAAI,EAAGA,EAAIsd,EAAWjf,OAAQ2B,IAAK,CACvC,IAAIwd,EAAIF,EAAWtd,GACfyd,EAAID,EAAEL,GACV,GAAGM,GAAmB,mBAAPA,EAAmB,CAC9B,IAAIC,EAAc,GAElBA,EAAYvc,MAAK,IAAIqF,MAAOmX,eAExBzS,EAAOtE,IACP8W,EAAYvc,KAAK,IAAM+J,EAAOtE,GAAK,KAGnC+V,GAAcA,EAAWC,WAAWve,OAAS,GAC7Cqf,EAAYvc,KAAK,IAAMwb,EAAWC,WAAa,OAGnD,IAAIgB,EAAeF,EAAYH,OAAO7U,GAEtC+U,EAAElU,KAAKiU,GAAG/C,MAAM+C,EAAGI,EACtB,CACJ,CACJ,CAcD,SAASzS,EAAOgS,EAAOvW,EAAI0W,EAAY9Y,GACnCrG,KAAKyI,GAAKA,EACVzI,KAAKqG,QAAUA,GAAW,CAAC,EAC3BrG,KAAKmf,WAAaA,EACdnf,KAAKmf,aACLnf,KAAKmf,WAAa,IAEtBnf,KAAKgf,MAAQ7C,EAAO6C,GAEpB,IADA,IAAIU,EAAUvf,OAAOC,KAAK+b,GAClBta,EAAI,EAAGA,EAAI6d,EAAQxf,OAAQ2B,IAC/B7B,KAAK0f,EAAQ7d,IACTkd,EAAI3T,KAAK,KAAMpL,KAAM0f,EAAQ7d,GAExC,CA/GDmL,EAAO2S,iBAAmB,SAAStZ,GAC/BiY,EAAgBjY,GAAW,CAAC,CAC/B,EAmHD2G,EAAOuO,UAAUqE,SAAW,SAAUZ,GAClChf,KAAKgf,MAAQ7C,EAAO6C,EACvB,EACDvf,EAAOD,QAAUwN,EAKjBA,EAAOmP,OAAS,CACZ0D,MAAO,QACPC,MAAO,QACPC,KAAM,OACNC,IAAK,MACLC,KAAM,OACNjD,MAAO,Q,gBC7LX,IAAIhQ,EAAS6O,EAAQ,GACjBC,EAAeD,EAAQ,KAwBvBqE,EAAY,CAAC,EAKbC,EAAU,GAKVC,EAAWpT,EAAOmP,OAAO0D,MAG7BpgB,EAAOD,QAAU,CAMb0e,mBAAoB,SAASpR,GACzBE,EAAOkR,mBAAmBpR,EAC7B,EAMDqR,sBAAuB,SAASrR,GAC5BE,EAAOmR,sBAAsBrR,EAChC,EAKD6S,iBAAkB,SAAStZ,GACvB2G,EAAO2S,iBAAiBtZ,EAC3B,EAKDga,UAAW,SAAS5X,EAAI0W,EAAY9Y,GAChC,IAAI0G,EAAS,IAAIC,EAAOoT,EAAU3X,EAAI0W,EAAY9Y,GAOlD,OANGoC,GACCyX,EAAUzX,GAAMyX,EAAUzX,IAAO,GACjCyX,EAAUzX,GAAIzF,KAAK+J,IAEnBoT,EAAQnd,KAAK+J,GAEVA,CACV,EAQDuT,gBAAiB,SAAStB,EAAOvW,GAE7B,IADA,IAAI6W,EAAI7W,EAAKyX,EAAUzX,IAAO,GAAM0X,EAC5Bte,EAAI,EAAGA,EAAIyd,EAAEpf,OAAQ2B,IACzByd,EAAEzd,GAAG+d,SAASZ,EAErB,EAKDuB,YAAa,SAAUvB,GACnBoB,EAAWpB,EAEX,IADA,IAAInd,EAAI,EACFA,EAAIse,EAAQjgB,OAAQ2B,IACtBse,EAAQte,GAAG+d,SAASZ,GAGxB,IAAI,IAAIvW,KAAMyX,EAAW,CACrB,IAAIZ,EAAIY,EAAUzX,IAAO,GACzB,IAAI5G,EAAI,EAAGA,EAAIyd,EAAEpf,OAAQ2B,IACrByd,EAAEzd,GAAG+d,SAASZ,EAErB,CACJ,EAID7C,OAAQnP,EAAOmP,OAIfL,aAAcA,E,uBCzGlB,IAOI0E,EAPAC,EAAuB,iBAAZC,QAAuBA,QAAU,KAC5CC,EAAeF,GAAwB,mBAAZA,EAAEnE,MAC7BmE,EAAEnE,MACF,SAAsBsE,EAAQC,EAAUtW,GACxC,OAAOuW,SAASvF,UAAUe,MAAM/U,KAAKqZ,EAAQC,EAAUtW,EACxD,EAIDiW,EADEC,GAA0B,mBAAdA,EAAEM,QACCN,EAAEM,QACV5gB,OAAO6gB,sBACC,SAAwBJ,GACvC,OAAOzgB,OAAO8gB,oBAAoBL,GAC/BxB,OAAOjf,OAAO6gB,sBAAsBJ,GACxC,EAEgB,SAAwBA,GACvC,OAAOzgB,OAAO8gB,oBAAoBL,EACnC,EAOH,IAAIM,EAAcC,OAAOC,OAAS,SAAqBpV,GACrD,OAAOA,GAAUA,CAClB,EAED,SAASlM,IACPA,EAAauhB,KAAK9Z,KAAKvH,KACxB,CACDP,EAAOD,QAAUM,EACjBL,EAAOD,QAAQ8hB,KAwYf,SAAcC,EAASpU,GACrB,OAAO,IAAInC,SAAQ,SAAUG,EAASF,GACpC,SAASuW,EAAcC,GACrBF,EAAQ3W,eAAeuC,EAAMuU,GAC7BzW,EAAOwW,EACR,CAED,SAASC,IAC+B,mBAA3BH,EAAQ3W,gBACjB2W,EAAQ3W,eAAe,QAAS4W,GAElCrW,EAAQ,GAAG8T,MAAM1X,KAAKgV,WACvB,CAEDoF,EAA+BJ,EAASpU,EAAMuU,EAAU,CAAEJ,MAAM,IACnD,UAATnU,GAMR,SAAuCoU,EAASvX,EAAS4X,GAC7B,mBAAfL,EAAQ7X,IACjBiY,EAA+BJ,EAAS,QAASvX,EAPO,CAAEsX,MAAM,GASnE,CATKO,CAA8BN,EAASC,EAE1C,GACF,EAxZD1hB,EAAaA,aAAeA,EAE5BA,EAAayb,UAAUuG,aAAUjc,EACjC/F,EAAayb,UAAUwG,aAAe,EACtCjiB,EAAayb,UAAUyG,mBAAgBnc,EAIvC,IAAIoc,EAAsB,GAE1B,SAASC,EAAclb,GACrB,GAAwB,mBAAbA,EACT,MAAM,IAAI2R,UAAU,0EAA4E3R,EAEnG,CAoCD,SAASmb,EAAiBC,GACxB,YAA2Bvc,IAAvBuc,EAAKJ,cACAliB,EAAamiB,oBACfG,EAAKJ,aACb,CAkDD,SAASK,EAAazB,EAAQ7W,EAAM/C,EAAUsb,GAC5C,IAAIxD,EACAxN,EACAiR,EA1HsBC,EAgJ1B,GApBAN,EAAclb,QAGCnB,KADfyL,EAASsP,EAAOkB,UAEdxQ,EAASsP,EAAOkB,QAAU3hB,OAAOsiB,OAAO,MACxC7B,EAAOmB,aAAe,SAIKlc,IAAvByL,EAAOoR,cACT9B,EAAOjf,KAAK,cAAeoI,EACf/C,EAASA,SAAWA,EAASA,SAAWA,GAIpDsK,EAASsP,EAAOkB,SAElBS,EAAWjR,EAAOvH,SAGHlE,IAAb0c,EAEFA,EAAWjR,EAAOvH,GAAQ/C,IACxB4Z,EAAOmB,kBAeT,GAbwB,mBAAbQ,EAETA,EAAWjR,EAAOvH,GAChBuY,EAAU,CAACtb,EAAUub,GAAY,CAACA,EAAUvb,GAErCsb,EACTC,EAASI,QAAQ3b,GAEjBub,EAASvf,KAAKgE,IAIhB8X,EAAIqD,EAAiBvB,IACb,GAAK2B,EAASriB,OAAS4e,IAAMyD,EAASK,OAAQ,CACpDL,EAASK,QAAS,EAGlB,IAAIC,EAAI,IAAI3X,MAAM,+CACEqX,EAASriB,OAAS,IAAMoM,OAAOvC,GADjC,qEAIlB8Y,EAAE1V,KAAO,8BACT0V,EAAEtB,QAAUX,EACZiC,EAAE9Y,KAAOA,EACT8Y,EAAEzF,MAAQmF,EAASriB,OA7KGsiB,EA8KHK,EA7KnBrhB,SAAWA,QAAQC,MAAMD,QAAQC,KAAK+gB,EA8KvC,CAGH,OAAO5B,CACR,CAaD,SAASkC,IACP,IAAK9iB,KAAK+iB,MAGR,OAFA/iB,KAAK4gB,OAAOhW,eAAe5K,KAAK+J,KAAM/J,KAAKgjB,QAC3ChjB,KAAK+iB,OAAQ,EACY,IAArBxG,UAAUrc,OACLF,KAAKgH,SAASO,KAAKvH,KAAK4gB,QAC1B5gB,KAAKgH,SAASsV,MAAMtc,KAAK4gB,OAAQrE,UAE3C,CAED,SAAS0G,EAAUrC,EAAQ7W,EAAM/C,GAC/B,IAAIkc,EAAQ,CAAEH,OAAO,EAAOC,YAAQnd,EAAW+a,OAAQA,EAAQ7W,KAAMA,EAAM/C,SAAUA,GACjFmc,EAAUL,EAAY1X,KAAK8X,GAG/B,OAFAC,EAAQnc,SAAWA,EACnBkc,EAAMF,OAASG,EACRA,CACR,CAyHD,SAASha,EAAWyX,EAAQ7W,EAAMqZ,GAChC,IAAI9R,EAASsP,EAAOkB,QAEpB,QAAejc,IAAXyL,EACF,MAAO,GAET,IAAI+R,EAAa/R,EAAOvH,GACxB,YAAmBlE,IAAfwd,EACK,GAEiB,mBAAfA,EACFD,EAAS,CAACC,EAAWrc,UAAYqc,GAAc,CAACA,GAElDD,EAsDT,SAAyBE,GAEvB,IADA,IAAIC,EAAM,IAAIpQ,MAAMmQ,EAAIpjB,QACf2B,EAAI,EAAGA,EAAI0hB,EAAIrjB,SAAU2B,EAChC0hB,EAAI1hB,GAAKyhB,EAAIzhB,GAAGmF,UAAYsc,EAAIzhB,GAElC,OAAO0hB,CACR,CA3DGC,CAAgBH,GAAcI,EAAWJ,EAAYA,EAAWnjB,OACnE,CAmBD,SAASwjB,EAAc3Z,GACrB,IAAIuH,EAAStR,KAAK8hB,QAElB,QAAejc,IAAXyL,EAAsB,CACxB,IAAI+R,EAAa/R,EAAOvH,GAExB,GAA0B,mBAAfsZ,EACT,OAAO,EACF,QAAmBxd,IAAfwd,EACT,OAAOA,EAAWnjB,MAErB,CAED,OAAO,CACR,CAMD,SAASujB,EAAWH,EAAK3iB,GAEvB,IADA,IAAIgjB,EAAO,IAAIxQ,MAAMxS,GACZkB,EAAI,EAAGA,EAAIlB,IAAKkB,EACvB8hB,EAAK9hB,GAAKyhB,EAAIzhB,GAChB,OAAO8hB,CACR,CA2CD,SAAShC,EAA+BJ,EAASpU,EAAMnG,EAAU4a,GAC/D,GAA0B,mBAAfL,EAAQ7X,GACbkY,EAAMN,KACRC,EAAQD,KAAKnU,EAAMnG,GAEnBua,EAAQ7X,GAAGyD,EAAMnG,OAEd,IAAwC,mBAA7Bua,EAAQ/Z,iBAYxB,MAAM,IAAImR,UAAU,6EAA+E4I,GATnGA,EAAQ/Z,iBAAiB2F,GAAM,SAASyW,EAAa7G,GAG/C6E,EAAMN,MACRC,EAAQpZ,oBAAoBgF,EAAMyW,GAEpC5c,EAAS+V,EACV,GAGF,CACF,CAraD5c,OAAO0jB,eAAe/jB,EAAc,sBAAuB,CACzDgkB,YAAY,EACZ7Z,IAAK,WACH,OAAOgY,CACR,EACD5c,IAAK,SAAS0X,GACZ,GAAmB,iBAARA,GAAoBA,EAAM,GAAKmE,EAAYnE,GACpD,MAAM,IAAIgH,WAAW,kGAAoGhH,EAAM,KAEjIkF,EAAsBlF,CACvB,IAGHjd,EAAauhB,KAAO,gBAEGxb,IAAjB7F,KAAK8hB,SACL9hB,KAAK8hB,UAAY3hB,OAAO6jB,eAAehkB,MAAM8hB,UAC/C9hB,KAAK8hB,QAAU3hB,OAAOsiB,OAAO,MAC7BziB,KAAK+hB,aAAe,GAGtB/hB,KAAKgiB,cAAgBhiB,KAAKgiB,oBAAiBnc,CAC5C,EAID/F,EAAayb,UAAU0I,gBAAkB,SAAyBtjB,GAChE,GAAiB,iBAANA,GAAkBA,EAAI,GAAKugB,EAAYvgB,GAChD,MAAM,IAAIojB,WAAW,gFAAkFpjB,EAAI,KAG7G,OADAX,KAAKgiB,cAAgBrhB,EACdX,IACR,EAQDF,EAAayb,UAAU2I,gBAAkB,WACvC,OAAO/B,EAAiBniB,KACzB,EAEDF,EAAayb,UAAU5Z,KAAO,SAAcoI,GAE1C,IADA,IAAIQ,EAAO,GACF1I,EAAI,EAAGA,EAAI0a,UAAUrc,OAAQ2B,IAAK0I,EAAKvH,KAAKuZ,UAAU1a,IAC/D,IAAIsiB,EAAoB,UAATpa,EAEXuH,EAAStR,KAAK8hB,QAClB,QAAejc,IAAXyL,EACF6S,EAAWA,QAA4Bte,IAAjByL,EAAOlH,WAC1B,IAAK+Z,EACR,OAAO,EAGT,GAAIA,EAAS,CACX,IAAIC,EAGJ,GAFI7Z,EAAKrK,OAAS,IAChBkkB,EAAK7Z,EAAK,IACR6Z,aAAclZ,MAGhB,MAAMkZ,EAGR,IAAI3C,EAAM,IAAIvW,MAAM,oBAAsBkZ,EAAK,KAAOA,EAAGxb,QAAU,IAAM,KAEzE,MADA6Y,EAAI4C,QAAUD,EACR3C,CACP,CAED,IAAIzX,EAAUsH,EAAOvH,GAErB,QAAgBlE,IAAZmE,EACF,OAAO,EAET,GAAuB,mBAAZA,EACT2W,EAAa3W,EAAShK,KAAMuK,OAE5B,KAAIuS,EAAM9S,EAAQ9J,OACdyG,EAAY8c,EAAWzZ,EAAS8S,GACpC,IAASjb,EAAI,EAAGA,EAAIib,IAAOjb,EACzB8e,EAAaha,EAAU9E,GAAI7B,KAAMuK,EAHnC,CAMF,OAAO,CACR,EAgEDzK,EAAayb,UAAU9R,YAAc,SAAqBM,EAAM/C,GAC9D,OAAOqb,EAAariB,KAAM+J,EAAM/C,GAAU,EAC3C,EAEDlH,EAAayb,UAAU7R,GAAK5J,EAAayb,UAAU9R,YAEnD3J,EAAayb,UAAU+I,gBACnB,SAAyBva,EAAM/C,GAC7B,OAAOqb,EAAariB,KAAM+J,EAAM/C,GAAU,EAC3C,EAoBLlH,EAAayb,UAAU+F,KAAO,SAAcvX,EAAM/C,GAGhD,OAFAkb,EAAclb,GACdhH,KAAK0J,GAAGK,EAAMkZ,EAAUjjB,KAAM+J,EAAM/C,IAC7BhH,IACR,EAEDF,EAAayb,UAAUgJ,oBACnB,SAA6Bxa,EAAM/C,GAGjC,OAFAkb,EAAclb,GACdhH,KAAKskB,gBAAgBva,EAAMkZ,EAAUjjB,KAAM+J,EAAM/C,IAC1ChH,IACR,EAGLF,EAAayb,UAAU3Q,eACnB,SAAwBb,EAAM/C,GAC5B,IAAIwd,EAAMlT,EAAQmT,EAAU5iB,EAAG6iB,EAK/B,GAHAxC,EAAclb,QAGCnB,KADfyL,EAAStR,KAAK8hB,SAEZ,OAAO9hB,KAGT,QAAa6F,KADb2e,EAAOlT,EAAOvH,IAEZ,OAAO/J,KAET,GAAIwkB,IAASxd,GAAYwd,EAAKxd,WAAaA,EACb,KAAtBhH,KAAK+hB,aACT/hB,KAAK8hB,QAAU3hB,OAAOsiB,OAAO,cAEtBnR,EAAOvH,GACVuH,EAAO1G,gBACT5K,KAAK2B,KAAK,iBAAkBoI,EAAMya,EAAKxd,UAAYA,SAElD,GAAoB,mBAATwd,EAAqB,CAGrC,IAFAC,GAAY,EAEP5iB,EAAI2iB,EAAKtkB,OAAS,EAAG2B,GAAK,EAAGA,IAChC,GAAI2iB,EAAK3iB,KAAOmF,GAAYwd,EAAK3iB,GAAGmF,WAAaA,EAAU,CACzD0d,EAAmBF,EAAK3iB,GAAGmF,SAC3Byd,EAAW5iB,EACX,KACD,CAGH,GAAI4iB,EAAW,EACb,OAAOzkB,KAEQ,IAAbykB,EACFD,EAAKG,QAiIf,SAAmBH,EAAMnO,GACvB,KAAOA,EAAQ,EAAImO,EAAKtkB,OAAQmW,IAC9BmO,EAAKnO,GAASmO,EAAKnO,EAAQ,GAC7BmO,EAAKI,KACN,CAnISC,CAAUL,EAAMC,GAGE,IAAhBD,EAAKtkB,SACPoR,EAAOvH,GAAQya,EAAK,SAEQ3e,IAA1ByL,EAAO1G,gBACT5K,KAAK2B,KAAK,iBAAkBoI,EAAM2a,GAAoB1d,EACzD,CAED,OAAOhH,IACR,EAELF,EAAayb,UAAUuJ,IAAMhlB,EAAayb,UAAU3Q,eAEpD9K,EAAayb,UAAUlR,mBACnB,SAA4BN,GAC1B,IAAIpD,EAAW2K,EAAQzP,EAGvB,QAAegE,KADfyL,EAAStR,KAAK8hB,SAEZ,OAAO9hB,KAGT,QAA8B6F,IAA1ByL,EAAO1G,eAUT,OATyB,IAArB2R,UAAUrc,QACZF,KAAK8hB,QAAU3hB,OAAOsiB,OAAO,MAC7BziB,KAAK+hB,aAAe,QACMlc,IAAjByL,EAAOvH,KACY,KAAtB/J,KAAK+hB,aACT/hB,KAAK8hB,QAAU3hB,OAAOsiB,OAAO,aAEtBnR,EAAOvH,IAEX/J,KAIT,GAAyB,IAArBuc,UAAUrc,OAAc,CAC1B,IACIQ,EADAN,EAAOD,OAAOC,KAAKkR,GAEvB,IAAKzP,EAAI,EAAGA,EAAIzB,EAAKF,SAAU2B,EAEjB,oBADZnB,EAAMN,EAAKyB,KAEX7B,KAAKqK,mBAAmB3J,GAK1B,OAHAV,KAAKqK,mBAAmB,kBACxBrK,KAAK8hB,QAAU3hB,OAAOsiB,OAAO,MAC7BziB,KAAK+hB,aAAe,EACb/hB,IACR,CAID,GAAyB,mBAFzB2G,EAAY2K,EAAOvH,IAGjB/J,KAAK4K,eAAeb,EAAMpD,QACrB,QAAkBd,IAAdc,EAET,IAAK9E,EAAI8E,EAAUzG,OAAS,EAAG2B,GAAK,EAAGA,IACrC7B,KAAK4K,eAAeb,EAAMpD,EAAU9E,IAIxC,OAAO7B,IACR,EAmBLF,EAAayb,UAAU5U,UAAY,SAAmBoD,GACpD,OAAOZ,EAAWnJ,KAAM+J,GAAM,EAC/B,EAEDjK,EAAayb,UAAUwJ,aAAe,SAAsBhb,GAC1D,OAAOZ,EAAWnJ,KAAM+J,GAAM,EAC/B,EAEDjK,EAAa4jB,cAAgB,SAASnC,EAASxX,GAC7C,MAAqC,mBAA1BwX,EAAQmC,cACVnC,EAAQmC,cAAc3Z,GAEtB2Z,EAAcnc,KAAKga,EAASxX,EAEtC,EAEDjK,EAAayb,UAAUmI,cAAgBA,EAiBvC5jB,EAAayb,UAAUyJ,WAAa,WAClC,OAAOhlB,KAAK+hB,aAAe,EAAIvB,EAAexgB,KAAK8hB,SAAW,EAC/D,C,GCxaGmD,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBtf,IAAjBuf,EACH,OAAOA,EAAa5lB,QAGrB,IAAIC,EAASwlB,EAAyBE,GAAY,CAGjD3lB,QAAS,CAAC,GAOX,OAHA6lB,EAAoBF,GAAU1lB,EAAQA,EAAOD,QAAS0lB,GAG/CzlB,EAAOD,OACf,C,OCrBA0lB,EAAoBvkB,EAAKlB,IACxB,IAAI6lB,EAAS7lB,GAAUA,EAAO8lB,WAC7B,IAAO9lB,EAAiB,QACxB,IAAM,EAEP,OADAylB,EAAoBM,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdJ,EAAoBM,EAAI,CAAChmB,EAASkmB,KACjC,IAAI,IAAIhlB,KAAOglB,EACXR,EAAoB5gB,EAAEohB,EAAYhlB,KAASwkB,EAAoB5gB,EAAE9E,EAASkB,IAC5EP,OAAO0jB,eAAerkB,EAASkB,EAAK,CAAEojB,YAAY,EAAM7Z,IAAKyb,EAAWhlB,IAE1E,ECNDwkB,EAAoB5gB,EAAI,CAACxB,EAAK6iB,IAAUxlB,OAAOob,UAAUC,eAAejU,KAAKzE,EAAK6iB,GCGxDT,EAAoB,I", "sources": ["webpack://JitsiMeetExternalAPI/webpack/universalModuleDefinition", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/js-utils/jitsi-local-storage/index.js", "webpack://JitsiMeetExternalAPI/./react/features/base/util/parseURLParams.ts", "webpack://JitsiMeetExternalAPI/./react/features/base/util/uri.ts", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/js-utils/transport/postis.js", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/js-utils/transport/PostMessageTransportBackend.js", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/js-utils/transport/constants.js", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/js-utils/transport/Transport.js", "webpack://JitsiMeetExternalAPI/./react/features/base/util/helpers.ts", "webpack://JitsiMeetExternalAPI/./modules/API/constants.js", "webpack://JitsiMeetExternalAPI/./modules/transport/index.js", "webpack://JitsiMeetExternalAPI/./modules/API/external/functions.js", "webpack://JitsiMeetExternalAPI/./modules/API/external/external_api.js", "webpack://JitsiMeetExternalAPI/./modules/API/external/index.js", "webpack://JitsiMeetExternalAPI/./node_modules/@hapi/bourne/lib/index.js", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/logger/lib/LogCollector.js", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/logger/lib/Logger.js", "webpack://JitsiMeetExternalAPI/./node_modules/@jitsi/logger/lib/index.js", "webpack://JitsiMeetExternalAPI/./node_modules/events/events.js", "webpack://JitsiMeetExternalAPI/webpack/bootstrap", "webpack://JitsiMeetExternalAPI/webpack/runtime/compat get default export", "webpack://JitsiMeetExternalAPI/webpack/runtime/define property getters", "webpack://JitsiMeetExternalAPI/webpack/runtime/hasOwnProperty shorthand", "webpack://JitsiMeetExternalAPI/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"JitsiMeetExternalAPI\"] = factory();\n\telse\n\t\troot[\"JitsiMeetExternalAPI\"] = factory();\n})(self, () => {\nreturn ", "import EventEmitter from 'events';\n\n/**\n * Dummy implementation of Storage interface.\n */\nclass DummyLocalStorage extends EventEmitter {\n\n    /**\n     * The object used for storage.\n     */\n    _storage = {};\n\n    /**\n     * Empties all keys out of the storage.\n     *\n     * @returns {void}\n     */\n    clear() {\n        this._storage = {};\n    }\n\n    /**\n     * Returns the number of data items stored in the Storage object.\n     *\n     * @returns {number} - The number of data items stored in the Storage object.\n     */\n    get length() {\n        return Object.keys(this._storage).length;\n    }\n\n    /**\n     * Will return that key's value associated to the passed key name.\n     *\n     * @param {string} keyName - The key name.\n     * @returns {*} - The key value.\n     */\n    getItem(keyName) {\n        return this._storage[keyName];\n    }\n\n    /**\n     * When passed a key name and value, will add that key to the storage,\n     * or update that key's value if it already exists.\n     *\n     * @param {string} keyName - The key name.\n     * @param {*} keyValue - The key value.\n     * @returns {void}\n     */\n    setItem(keyName, keyValue) {\n        this._storage[keyName] = keyValue;\n    }\n\n    /**\n     * When passed a key name, will remove that key from the storage.\n     *\n     * @param {string} keyName - The key name.\n     * @returns {void}\n     */\n    removeItem(keyName) {\n        delete this._storage[keyName];\n    }\n\n    /**\n     * When passed a number n, this method will return the name of the nth key in the storage.\n     *\n     * @param {number} idx - The index of the key.\n     * @returns {string} - The nth key name.\n     */\n    key(n) {\n        const keys = Object.keys(this._storage);\n\n        if (keys.length <= n) {\n            return undefined;\n        }\n\n        return keys[n];\n    }\n\n    /**\n     * Serializes the content of the storage.\n     *\n     * @param {Array<string>} ignore - Array with keys from the local storage to be ignored.\n     * @returns {string} - The serialized content.\n     */\n    serialize(ignore = []) {\n        if (ignore.length === 0) {\n            return JSON.stringify(this._storage);\n        }\n\n        const storageCopy = { ...this._storage };\n\n        ignore.forEach(key => {\n            delete storageCopy[key];\n        });\n\n        return JSON.stringify(storageCopy);\n    }\n}\n\n/**\n * Wrapper class for browser's local storage object.\n */\nclass JitsiLocalStorage extends EventEmitter {\n    /**\n     * @constructor\n     * @param {Storage} storage browser's local storage object.\n     */\n    constructor() {\n        super();\n\n        try {\n            this._storage = window.localStorage;\n            this._localStorageDisabled = false;\n        } catch (ignore) {\n            // localStorage throws an exception.\n        }\n\n        if (!this._storage) { // Handles the case when window.localStorage is undefined or throws an exception.\n            console.warn('Local storage is disabled.');\n            this._storage = new DummyLocalStorage();\n            this._localStorageDisabled = true;\n        }\n    }\n\n    /**\n     * Returns true if window.localStorage is disabled and false otherwise.\n     *\n     * @returns {boolean} - True if window.localStorage is disabled and false otherwise.\n     */\n    isLocalStorageDisabled() {\n        return this._localStorageDisabled;\n    }\n\n    /**\n     * Empties all keys out of the storage.\n     *\n     * @returns {void}\n     */\n    clear() {\n        this._storage.clear();\n        this.emit('changed');\n    }\n\n    /**\n     * Returns the number of data items stored in the Storage object.\n     *\n     * @returns {number} - The number of data items stored in the Storage object.\n     */\n    get length() {\n        return this._storage.length;\n    }\n\n    /**\n     * Returns that passed key's value.\n     * @param {string} keyName the name of the key you want to retrieve\n     * the value of.\n     * @returns {String|null} the value of the key. If the key does not exist,\n     * null is returned.\n     */\n    getItem(keyName) {\n        return this._storage.getItem(keyName);\n    }\n\n    /**\n     * Adds a key to the storage, or update key's value if it already exists.\n     * @param {string} keyName - the name of the key you want to create/update.\n     * @param {string} keyValue - the value you want to give the key you are\n     * creating/updating.\n     * @param {boolean} dontEmitChangedEvent - If true a changed event won't be emitted.\n     */\n    setItem(keyName, keyValue, dontEmitChangedEvent = false) {\n        this._storage.setItem(keyName, keyValue);\n\n        if (!dontEmitChangedEvent) {\n            this.emit('changed');\n        }\n    }\n\n    /**\n     * Remove a key from the storage.\n     * @param {string} keyName the name of the key you want to remove.\n     */\n    removeItem(keyName) {\n        this._storage.removeItem(keyName);\n        this.emit('changed');\n    }\n\n    /**\n     * Returns the name of the nth key in the list, or null if n is greater\n     * than or equal to the number of key/value pairs in the object.\n     *\n     * @param {number} i - The index of the key in the list.\n     * @returns {string}\n     */\n    key(i) {\n        return this._storage.key(i);\n    }\n\n    /**\n     * Serializes the content of the storage.\n     *\n     * @param {Array<string>} ignore - Array with keys from the local storage to be ignored.\n     * @returns {string} - The serialized content.\n     */\n    serialize(ignore = []) {\n        if (this.isLocalStorageDisabled()) {\n            return this._storage.serialize(ignore);\n        }\n\n        const length = this._storage.length;\n        const localStorageContent = {};\n\n        for (let i = 0; i < length; i++) {\n            const key = this._storage.key(i);\n\n            if (!ignore.includes(key)) {\n                localStorageContent[key] = this._storage.getItem(key);\n            }\n        }\n\n        return JSON.stringify(localStorageContent);\n    }\n}\n\nexport const jitsiLocalStorage = new JitsiLocalStorage();\n", "// @ts-expect-error\nimport Bourne from '@hapi/bourne';\nimport { reportError } from './helpers';\n/**\n * A list if keys to ignore when parsing.\n *\n * @type {string[]}\n */\nconst blacklist = ['__proto__', 'constructor', 'prototype'];\n/**\n * Parses the query/search or fragment/hash parameters out of a specific URL and\n * returns them as a JS object.\n *\n * @param {URL} url - The URL to parse.\n * @param {boolean} dontParse - If falsy, some transformations (for parsing the\n * value as JSON) will be executed.\n * @param {string} source - If {@code 'search'}, the parameters will parsed out\n * of {@code url.search}; otherwise, out of {@code url.hash}.\n * @returns {Object}\n */\nexport function parseURLParams(url, dontParse = false, source = 'hash') {\n    if (typeof url === 'string') {\n        // eslint-disable-next-line no-param-reassign\n        url = new URL(url);\n    }\n    const paramStr = source === 'search' ? url.search : url.hash;\n    const params = {};\n    const paramParts = paramStr?.substr(1).split('&') || [];\n    // Detect and ignore hash params for hash routers.\n    if (source === 'hash' && paramParts.length === 1) {\n        const firstParam = paramParts[0];\n        if (firstParam.startsWith('/') && firstParam.split('&').length === 1) {\n            return params;\n        }\n    }\n    paramParts.forEach((part) => {\n        const param = part.split('=');\n        const key = param[0];\n        if (!key || key.split('.').some((k) => blacklist.includes(k))) {\n            return;\n        }\n        let value;\n        try {\n            value = param[1];\n            if (!dontParse) {\n                const decoded = decodeURIComponent(value).replace(/\\\\&/, '&');\n                value = decoded === 'undefined' ? undefined : Bourne.parse(decoded);\n            }\n        }\n        catch (e) {\n            reportError(e, `Failed to parse URL parameter value: ${String(value)}`);\n            return;\n        }\n        params[key] = value;\n    });\n    return params;\n}\n", "import { parseURLParams } from './parseURLParams';\nimport { normalizeNFKC } from './strings';\n/**\n * Http status codes.\n */\nexport var StatusCode;\n(function (StatusCode) {\n    StatusCode[StatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n})(StatusCode || (StatusCode = {}));\n/**\n * The app linking scheme.\n * TODO: This should be read from the manifest files later.\n */\nexport const APP_LINK_SCHEME = 'org.jitsi.meet:';\n/**\n * A list of characters to be excluded/removed from the room component/segment\n * of a conference/meeting URI/URL. The list is based on RFC 3986 and the jxmpp\n * library utilized by jicofo.\n */\nconst _ROOM_EXCLUDE_PATTERN = '[\\\\:\\\\?#\\\\[\\\\]@!$&\\'()*+,;=></\"]';\n/**\n * The {@link RegExp} pattern of the authority of a URI.\n *\n * @private\n * @type {string}\n */\nconst _URI_AUTHORITY_PATTERN = '(//[^/?#]+)';\n/**\n * The {@link RegExp} pattern of the path of a URI.\n *\n * @private\n * @type {string}\n */\nconst _URI_PATH_PATTERN = '([^?#]*)';\n/**\n * The {@link RegExp} pattern of the protocol of a URI.\n *\n * FIXME: The URL class exposed by JavaScript will not include the colon in\n * the protocol field. Also in other places (at the time of this writing:\n * the DeepLinkingMobilePage.js) the APP_LINK_SCHEME does not include\n * the double dots, so things are inconsistent.\n *\n * @type {string}\n */\nexport const URI_PROTOCOL_PATTERN = '^([a-z][a-z0-9\\\\.\\\\+-]*:)';\n/**\n * Excludes/removes certain characters from a specific path part which are\n * incompatible with Jitsi Meet on the client and/or server sides. The main\n * use case for this method is to clean up the room name and the tenant.\n *\n * @param {?string} pathPart - The path part to fix.\n * @private\n * @returns {?string}\n */\nfunction _fixPathPart(pathPart) {\n    return pathPart\n        ? pathPart.replace(new RegExp(_ROOM_EXCLUDE_PATTERN, 'g'), '')\n        : pathPart;\n}\n/**\n * Fixes the scheme part of a specific URI (string) so that it contains a\n * well-known scheme such as HTTP(S). For example, the mobile app implements an\n * app-specific URI scheme in addition to Universal Links. The app-specific\n * scheme may precede or replace the well-known scheme. In such a case, dealing\n * with the app-specific scheme only complicates the logic and it is simpler to\n * get rid of it (by translating the app-specific scheme into a well-known\n * scheme).\n *\n * @param {string} uri - The URI (string) to fix the scheme of.\n * @private\n * @returns {string}\n */\nfunction _fixURIStringScheme(uri) {\n    const regex = new RegExp(`${URI_PROTOCOL_PATTERN}+`, 'gi');\n    const match = regex.exec(uri);\n    if (match) {\n        // As an implementation convenience, pick up the last scheme and make\n        // sure that it is a well-known one.\n        let protocol = match[match.length - 1].toLowerCase();\n        if (protocol !== 'http:' && protocol !== 'https:') {\n            protocol = 'https:';\n        }\n        /* eslint-disable no-param-reassign */\n        uri = uri.substring(regex.lastIndex);\n        if (uri.startsWith('//')) {\n            // The specified URL was not a room name only, it contained an\n            // authority.\n            uri = protocol + uri;\n        }\n        /* eslint-enable no-param-reassign */\n    }\n    return uri;\n}\n/**\n * Converts a path to a backend-safe format, by splitting the path '/' processing each part.\n * Properly lowercased and url encoded.\n *\n * @param {string?} path - The path to convert.\n * @returns {string?}\n */\nexport function getBackendSafePath(path) {\n    if (!path) {\n        return path;\n    }\n    return path\n        .split('/')\n        .map(getBackendSafeRoomName)\n        .join('/');\n}\n/**\n * Converts a room name to a backend-safe format. Properly lowercased and url encoded.\n *\n * @param {string?} room - The room name to convert.\n * @returns {string?}\n */\nexport function getBackendSafeRoomName(room) {\n    if (!room) {\n        return room;\n    }\n    /* eslint-disable no-param-reassign */\n    try {\n        // We do not know if we get an already encoded string at this point\n        // as different platforms do it differently, but we need a decoded one\n        // for sure. However since decoding a non-encoded string is a noop, we're safe\n        // doing it here.\n        room = decodeURIComponent(room);\n    }\n    catch (e) {\n        // This can happen though if we get an unencoded string and it contains\n        // some characters that look like an encoded entity, but it's not.\n        // But in this case we're fine going on...\n    }\n    // Normalize the character set.\n    room = normalizeNFKC(room);\n    // Only decoded and normalized strings can be lowercased properly.\n    room = room?.toLowerCase();\n    // But we still need to (re)encode it.\n    room = encodeURIComponent(room ?? '');\n    /* eslint-enable no-param-reassign */\n    // Unfortunately we still need to lowercase it, because encoding a string will\n    // add some uppercase characters, but some backend services\n    // expect it to be full lowercase. However lowercasing an encoded string\n    // doesn't change the string value.\n    return room.toLowerCase();\n}\n/**\n * Gets the (Web application) context root defined by a specific location (URI).\n *\n * @param {Object} location - The location (URI) which defines the (Web\n * application) context root.\n * @public\n * @returns {string} - The (Web application) context root defined by the\n * specified {@code location} (URI).\n */\nexport function getLocationContextRoot({ pathname }) {\n    const contextRootEndIndex = pathname.lastIndexOf('/');\n    return (contextRootEndIndex === -1\n        ? '/'\n        : pathname.substring(0, contextRootEndIndex + 1));\n}\n/**\n * Constructs a new {@code Array} with URL parameter {@code String}s out of a\n * specific {@code Object}.\n *\n * @param {Object} obj - The {@code Object} to turn into URL parameter\n * {@code String}s.\n * @returns {Array<string>} The {@code Array} with URL parameter {@code String}s\n * constructed out of the specified {@code obj}.\n */\nfunction _objectToURLParamsArray(obj = {}) {\n    const params = [];\n    for (const key in obj) { // eslint-disable-line guard-for-in\n        try {\n            params.push(`${key}=${encodeURIComponent(JSON.stringify(obj[key]))}`);\n        }\n        catch (e) {\n            console.warn(`Error encoding ${key}: ${e}`);\n        }\n    }\n    return params;\n}\n/**\n * Parses a specific URI string into an object with the well-known properties of\n * the {@link Location} and/or {@link URL} interfaces implemented by Web\n * browsers. The parsing attempts to be in accord with IETF's RFC 3986.\n *\n * @param {string} str - The URI string to parse.\n * @public\n * @returns {{\n *     hash: string,\n *     host: (string|undefined),\n *     hostname: (string|undefined),\n *     pathname: string,\n *     port: (string|undefined),\n *     protocol: (string|undefined),\n *     search: string\n * }}\n */\nexport function parseStandardURIString(str) {\n    /* eslint-disable no-param-reassign */\n    const obj = {\n        toString: _standardURIToString\n    };\n    let regex;\n    let match;\n    // XXX A URI string as defined by RFC 3986 does not contain any whitespace.\n    // Usually, a browser will have already encoded any whitespace. In order to\n    // avoid potential later problems related to whitespace in URI, strip any\n    // whitespace. Anyway, the Jitsi Meet app is not known to utilize unencoded\n    // whitespace so the stripping is deemed safe.\n    str = str.replace(/\\s/g, '');\n    // protocol\n    regex = new RegExp(URI_PROTOCOL_PATTERN, 'gi');\n    match = regex.exec(str);\n    if (match) {\n        obj.protocol = match[1].toLowerCase();\n        str = str.substring(regex.lastIndex);\n    }\n    // authority\n    regex = new RegExp(`^${_URI_AUTHORITY_PATTERN}`, 'gi');\n    match = regex.exec(str);\n    if (match) {\n        let authority = match[1].substring(/* // */ 2);\n        str = str.substring(regex.lastIndex);\n        // userinfo\n        const userinfoEndIndex = authority.indexOf('@');\n        if (userinfoEndIndex !== -1) {\n            authority = authority.substring(userinfoEndIndex + 1);\n        }\n        obj.host = authority;\n        // port\n        const portBeginIndex = authority.lastIndexOf(':');\n        if (portBeginIndex !== -1) {\n            obj.port = authority.substring(portBeginIndex + 1);\n            authority = authority.substring(0, portBeginIndex);\n        }\n        // hostname\n        obj.hostname = authority;\n    }\n    // pathname\n    regex = new RegExp(`^${_URI_PATH_PATTERN}`, 'gi');\n    match = regex.exec(str);\n    let pathname;\n    if (match) {\n        pathname = match[1];\n        str = str.substring(regex.lastIndex);\n    }\n    if (pathname) {\n        pathname.startsWith('/') || (pathname = `/${pathname}`);\n    }\n    else {\n        pathname = '/';\n    }\n    obj.pathname = pathname;\n    // query\n    if (str.startsWith('?')) {\n        let hashBeginIndex = str.indexOf('#', 1);\n        if (hashBeginIndex === -1) {\n            hashBeginIndex = str.length;\n        }\n        obj.search = str.substring(0, hashBeginIndex);\n        str = str.substring(hashBeginIndex);\n    }\n    else {\n        obj.search = ''; // Google Chrome\n    }\n    // fragment\n    obj.hash = str.startsWith('#') ? str : '';\n    /* eslint-enable no-param-reassign */\n    return obj;\n}\n/**\n * Parses a specific URI which (supposedly) references a Jitsi Meet resource\n * (location).\n *\n * @param {(string|undefined)} uri - The URI to parse which (supposedly)\n * references a Jitsi Meet resource (location).\n * @public\n * @returns {{\n *     contextRoot: string,\n *     hash: string,\n *     host: string,\n *     hostname: string,\n *     pathname: string,\n *     port: string,\n *     protocol: string,\n *     room: (string|undefined),\n *     search: string\n * }}\n */\nexport function parseURIString(uri) {\n    if (typeof uri !== 'string') {\n        return undefined;\n    }\n    const obj = parseStandardURIString(_fixURIStringScheme(uri));\n    // XXX While the components/segments of pathname are URI encoded, Jitsi Meet\n    // on the client and/or server sides still don't support certain characters.\n    obj.pathname = obj.pathname.split('/').map((pathPart) => _fixPathPart(pathPart))\n        .join('/');\n    // Add the properties that are specific to a Jitsi Meet resource (location)\n    // such as contextRoot, room:\n    // contextRoot\n    // @ts-ignore\n    obj.contextRoot = getLocationContextRoot(obj);\n    // The room (name) is the last component/segment of pathname.\n    const { pathname } = obj;\n    const contextRootEndIndex = pathname.lastIndexOf('/');\n    obj.room = pathname.substring(contextRootEndIndex + 1) || undefined;\n    if (contextRootEndIndex > 1) {\n        // The part of the pathname from the beginning to the room name is the tenant.\n        obj.tenant = pathname.substring(1, contextRootEndIndex);\n    }\n    return obj;\n}\n/**\n * Implements {@code href} and {@code toString} for the {@code Object} returned\n * by {@link #parseStandardURIString}.\n *\n * @param {Object} [thiz] - An {@code Object} returned by\n * {@code #parseStandardURIString} if any; otherwise, it is presumed that the\n * function is invoked on such an instance.\n * @returns {string}\n */\nfunction _standardURIToString(thiz) {\n    // @ts-ignore\n    // eslint-disable-next-line @typescript-eslint/no-invalid-this\n    const { hash, host, pathname, protocol, search } = thiz || this;\n    let str = '';\n    protocol && (str += protocol);\n    // TODO userinfo\n    host && (str += `//${host}`);\n    str += pathname || '/';\n    search && (str += search);\n    hash && (str += hash);\n    return str;\n}\n/**\n * Sometimes we receive strings that we don't know if already percent-encoded, or not, due to the\n * various sources we get URLs or room names. This function encapsulates the decoding in a safe way.\n *\n * @param {string} text - The text to decode.\n * @returns {string}\n */\nexport function safeDecodeURIComponent(text) {\n    try {\n        return decodeURIComponent(text);\n    }\n    catch (e) {\n        // The text wasn't encoded.\n    }\n    return text;\n}\n/**\n * Attempts to return a {@code String} representation of a specific\n * {@code Object} which is supposed to represent a URL. Obviously, if a\n * {@code String} is specified, it is returned. If a {@code URL} is specified,\n * its {@code URL#href} is returned. Additionally, an {@code Object} similar to\n * the one accepted by the constructor of Web's ExternalAPI is supported on both\n * mobile/React Native and Web/React.\n *\n * @param {Object|string} obj - The URL to return a {@code String}\n * representation of.\n * @returns {string} - A {@code String} representation of the specified\n * {@code obj} which is supposed to represent a URL.\n */\nexport function toURLString(obj) {\n    let str;\n    switch (typeof obj) {\n        case 'object':\n            if (obj) {\n                if (obj instanceof URL) {\n                    str = obj.href;\n                }\n                else {\n                    str = urlObjectToString(obj);\n                }\n            }\n            break;\n        case 'string':\n            str = String(obj);\n            break;\n    }\n    return str;\n}\n/**\n * Attempts to return a {@code String} representation of a specific\n * {@code Object} similar to the one accepted by the constructor\n * of Web's ExternalAPI.\n *\n * @param {Object} o - The URL to return a {@code String} representation of.\n * @returns {string} - A {@code String} representation of the specified\n * {@code Object}.\n */\nexport function urlObjectToString(o) {\n    // First normalize the given url. It come as o.url or split into o.serverURL\n    // and o.room.\n    let tmp;\n    if (o.serverURL && o.room) {\n        tmp = new URL(o.room, o.serverURL).toString();\n    }\n    else if (o.room) {\n        tmp = o.room;\n    }\n    else {\n        tmp = o.url || '';\n    }\n    const url = parseStandardURIString(_fixURIStringScheme(tmp));\n    // protocol\n    if (!url.protocol) {\n        let protocol = o.protocol || o.scheme;\n        if (protocol) {\n            // Protocol is supposed to be the scheme and the final ':'. Anyway,\n            // do not make a fuss if the final ':' is not there.\n            protocol.endsWith(':') || (protocol += ':');\n            url.protocol = protocol;\n        }\n    }\n    // authority & pathname\n    let { pathname } = url;\n    if (!url.host) {\n        // Web's ExternalAPI domain\n        //\n        // It may be host/hostname and pathname with the latter denoting the\n        // tenant.\n        const domain = o.domain || o.host || o.hostname;\n        if (domain) {\n            const { host, hostname, pathname: contextRoot, port } = parseStandardURIString(\n            // XXX The value of domain in supposed to be host/hostname\n            // and, optionally, pathname. Make sure it is not taken for\n            // a pathname only.\n            _fixURIStringScheme(`${APP_LINK_SCHEME}//${domain}`));\n            // authority\n            if (host) {\n                url.host = host;\n                url.hostname = hostname;\n                url.port = port;\n            }\n            // pathname\n            pathname === '/' && contextRoot !== '/' && (pathname = contextRoot);\n        }\n    }\n    // pathname\n    // Web's ExternalAPI roomName\n    const room = o.roomName || o.room;\n    if (room\n        && (url.pathname.endsWith('/')\n            || !url.pathname.endsWith(`/${room}`))) {\n        pathname.endsWith('/') || (pathname += '/');\n        pathname += room;\n    }\n    url.pathname = pathname;\n    // query/search\n    // Web's ExternalAPI jwt and lang\n    const { jwt, lang, release } = o;\n    const search = new URLSearchParams(url.search);\n    if (jwt) {\n        search.set('jwt', jwt);\n    }\n    const { defaultLanguage } = o.configOverwrite || {};\n    if (lang || defaultLanguage) {\n        search.set('lang', lang || defaultLanguage);\n    }\n    if (release) {\n        search.set('release', release);\n    }\n    const searchString = search.toString();\n    if (searchString) {\n        url.search = `?${searchString}`;\n    }\n    // fragment/hash\n    let { hash } = url;\n    for (const urlPrefix of ['config', 'interfaceConfig', 'devices', 'userInfo', 'appData']) {\n        const urlParamsArray = _objectToURLParamsArray(o[`${urlPrefix}Overwrite`]\n            || o[urlPrefix]\n            || o[`${urlPrefix}Override`]);\n        if (urlParamsArray.length) {\n            let urlParamsString = `${urlPrefix}.${urlParamsArray.join(`&${urlPrefix}.`)}`;\n            if (hash.length) {\n                urlParamsString = `&${urlParamsString}`;\n            }\n            else {\n                hash = '#';\n            }\n            hash += urlParamsString;\n        }\n    }\n    url.hash = hash;\n    return url.toString() || undefined;\n}\n/**\n * Adds hash params to URL.\n *\n * @param {URL} url - The URL.\n * @param {Object} hashParamsToAdd - A map with the parameters to be set.\n * @returns {URL} - The new URL.\n */\nexport function addHashParamsToURL(url, hashParamsToAdd = {}) {\n    const params = parseURLParams(url);\n    const urlParamsArray = _objectToURLParamsArray({\n        ...params,\n        ...hashParamsToAdd\n    });\n    if (urlParamsArray.length) {\n        url.hash = `#${urlParamsArray.join('&')}`;\n    }\n    return url;\n}\n/**\n * Returns the decoded URI.\n *\n * @param {string} uri - The URI to decode.\n * @returns {string}\n */\nexport function getDecodedURI(uri) {\n    return decodeURI(uri.replace(/^https?:\\/\\//i, ''));\n}\n/**\n * Adds new param to a url string. Checks whether to use '?' or '&' as a separator (checks for already existing params).\n *\n * @param {string} url - The url to modify.\n * @param {string} name - The param name to add.\n * @param {string} value - The value for the param.\n *\n * @returns {string} - The modified url.\n */\nexport function appendURLParam(url, name, value) {\n    const newUrl = new URL(url);\n    newUrl.searchParams.append(name, value);\n    return newUrl.toString();\n}\n", "/* eslint-disable */\n\n// Originally: https://github.com/adtile/postis\n//\n// The MIT License\n// \n// Copyright (c) 2015-2015 Adtile Technologies Inc. http://www.adtile.me\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nfunction Postis(options) {\n  var scope = options.scope;\n  var targetWindow = options.window;\n  var windowForEventListening = options.windowForEventListening || window;\n  var allowedOrigin = options.allowedOrigin;\n  var listeners = {};\n  var sendBuffer = [];\n  var listenBuffer = {};\n  var ready = false;\n  var readyMethod = \"__ready__\";\n  var readynessCheck;\n\n  var listener = function(event) {\n    var data;\n    try {\n      data = JSON.parse(event.data);\n    } catch (e) {\n      return;\n    }\n\n    if (allowedOrigin && event.origin !== allowedOrigin) {\n        return;\n    }\n\n    if (data && data.postis && data.scope === scope) {\n      var listenersForMethod = listeners[data.method];\n      if (listenersForMethod) {\n        for (var i = 0; i < listenersForMethod.length; i++) {\n          listenersForMethod[i].call(null, data.params);\n        }\n      } else {\n        listenBuffer[data.method] = listenBuffer[data.method] || [];\n        listenBuffer[data.method].push(data.params);\n      }\n    }\n  };\n\n  windowForEventListening.addEventListener(\"message\", listener, false);\n\n  var postis = {\n    listen: function (method, callback) {\n      listeners[method] = listeners[method] || [];\n      listeners[method].push(callback);\n\n      var listenBufferForMethod = listenBuffer[method];\n      if (listenBufferForMethod) {\n        var listenersForMethod = listeners[method];\n        for (var i = 0; i < listenersForMethod.length; i++) {\n          for (var j = 0; j < listenBufferForMethod.length; j++) {\n            listenersForMethod[i].call(null, listenBufferForMethod[j]);\n          }\n        }\n      }\n      delete listenBuffer[method];\n    },\n\n    send: function (opts) {\n      var method = opts.method;\n\n      if ((ready || opts.method === readyMethod) && (targetWindow && typeof targetWindow.postMessage === \"function\")) {\n        targetWindow.postMessage(JSON.stringify({\n          postis: true,\n          scope: scope,\n          method: method,\n          params: opts.params\n        }), \"*\");\n      } else {\n        sendBuffer.push(opts);\n      }\n    },\n\n    ready: function (callback) {\n      if (ready) {\n        callback();\n      } else {\n        setTimeout(function () { postis.ready(callback); }, 50);\n      }\n    },\n\n    destroy: function (callback) {\n      clearInterval(readynessCheck);\n      ready = false;\n      if (windowForEventListening && typeof windowForEventListening.removeEventListener === \"function\") {\n        windowForEventListening.removeEventListener(\"message\", listener);\n      }\n      callback && callback();\n    }\n  };\n\n  var readyCheckID = +new Date() + Math.random() + \"\";\n\n  readynessCheck = setInterval(function () {\n    postis.send({\n      method: readyMethod,\n      params: readyCheckID\n    });\n  }, 50);\n\n  postis.listen(readyMethod, function (id) {\n    if (id === readyCheckID) {\n      clearInterval(readynessCheck);\n      ready = true;\n\n      for (var i = 0; i < sendBuffer.length; i++) {\n        postis.send(sendBuffer[i]);\n      }\n      sendBuffer = [];\n    } else {\n      postis.send({\n        method: readyMethod,\n        params: id\n      });\n    }\n  });\n\n  return postis;\n}\n\nexport default Postis;\n", "import Postis from './postis.js';\n\n/**\n * The default options for postis.\n *\n * @type {Object}\n */\nconst DEFAULT_POSTIS_OPTIONS = {\n    window: window.opener || window.parent\n};\n\n/**\n * The postis method used for all messages.\n *\n * @type {string}\n */\nconst POSTIS_METHOD_NAME = 'message';\n\n/**\n * Implements message transport using the postMessage API.\n */\nexport default class PostMessageTransportBackend {\n    /**\n     * Creates new PostMessageTransportBackend instance.\n     *\n     * @param {Object} options - Optional parameters for configuration of the\n     * transport.\n     */\n    constructor({ postisOptions } = {}) {\n        // eslint-disable-next-line new-cap\n        this.postis = Postis({\n            ...DEFAULT_POSTIS_OPTIONS,\n            ...postisOptions\n        });\n\n        this._receiveCallback = () => {\n            // Do nothing until a callback is set by the consumer of\n            // PostMessageTransportBackend via setReceiveCallback.\n        };\n\n        this.postis.listen(\n            POSTIS_METHOD_NAME,\n            message => this._receiveCallback(message));\n    }\n\n    /**\n     * Disposes the allocated resources.\n     *\n     * @returns {void}\n     */\n    dispose() {\n        this.postis.destroy();\n    }\n\n    /**\n     * Sends the passed message.\n     *\n     * @param {Object} message - The message to be sent.\n     * @returns {void}\n     */\n    send(message) {\n        this.postis.send({\n            method: POSTIS_METHOD_NAME,\n            params: message\n        });\n    }\n\n    /**\n     * Sets the callback for receiving data.\n     *\n     * @param {Function} callback - The new callback.\n     * @returns {void}\n     */\n    setReceiveCallback(callback) {\n        this._receiveCallback = callback;\n    }\n}\n", "/**\n * The message type for events.\n *\n * @type {string}\n */\nexport const MESSAGE_TYPE_EVENT = 'event';\n\n/**\n * The message type for requests.\n *\n * @type {string}\n */\nexport const MESSAGE_TYPE_REQUEST = 'request';\n\n/**\n * The message type for responses.\n *\n * @type {string}\n */\nexport const MESSAGE_TYPE_RESPONSE = 'response';\n", "import {\n    MESSAGE_TYPE_EVENT,\n    MESSAGE_TYPE_REQUEST,\n    MESSAGE_TYPE_RESPONSE\n} from './constants.js';\n\n/**\n* Stores the currnet transport backend that have to be used. Also implements\n* request/response mechanism.\n*/\nexport default class Transport {\n    /**\n     * Creates new instance.\n     *\n     * @param {Object} options - Optional parameters for configuration of the\n     * transport backend.\n     */\n    constructor({ backend } = {}) {\n        /**\n         * Maps an event name and listener that have been added to the Transport\n         * instance.\n         *\n         * @type {Map<string, Function>}\n         */\n        this._listeners = new Map();\n\n        /**\n         * The request ID counter used for the id property of the request. This\n         * property is used to match the responses with the request.\n         *\n         * @type {number}\n         */\n        this._requestID = 0;\n\n        /**\n         * Maps an IDs of the requests and handlers that will process the\n         * responses of those requests.\n         *\n         * @type {Map<number, Function>}\n         */\n        this._responseHandlers = new Map();\n\n        /**\n         * A set with the events and requests that were received but not\n         * processed by any listener. They are later passed on every new\n         * listener until they are processed.\n         *\n         * @type {Set<Object>}\n         */\n        this._unprocessedMessages = new Set();\n\n        /**\n         * Alias.\n         */\n        this.addListener = this.on;\n\n        if (backend) {\n            this.setBackend(backend);\n        }\n    }\n\n    /**\n     * Disposes the current transport backend.\n     *\n     * @returns {void}\n     */\n    _disposeBackend() {\n        if (this._backend) {\n            this._backend.dispose();\n            this._backend = null;\n        }\n    }\n\n    /**\n     * Handles incoming messages from the transport backend.\n     *\n     * @param {Object} message - The message.\n     * @returns {void}\n     */\n    _onMessageReceived(message) {\n        if (message.type === MESSAGE_TYPE_RESPONSE) {\n            const handler = this._responseHandlers.get(message.id);\n\n            if (handler) {\n                handler(message);\n                this._responseHandlers.delete(message.id);\n            }\n        } else if (message.type === MESSAGE_TYPE_REQUEST) {\n            this.emit('request', message.data, (result, error) => {\n                this._backend.send({\n                    type: MESSAGE_TYPE_RESPONSE,\n                    error,\n                    id: message.id,\n                    result\n                });\n            });\n        } else {\n            this.emit('event', message.data);\n        }\n    }\n\n    /**\n     * Disposes the allocated resources.\n     *\n     * @returns {void}\n     */\n    dispose() {\n        this._responseHandlers.clear();\n        this._unprocessedMessages.clear();\n        this.removeAllListeners();\n        this._disposeBackend();\n    }\n\n    /**\n     * Calls each of the listeners registered for the event named eventName, in\n     * the order they were registered, passing the supplied arguments to each.\n     *\n     * @param {string} eventName -  The name of the event.\n     * @returns {boolean} True if the event has been processed by any listener,\n     * false otherwise.\n     */\n    emit(eventName, ...args) {\n        const listenersForEvent = this._listeners.get(eventName);\n        let isProcessed = false;\n\n        if (listenersForEvent && listenersForEvent.size) {\n            listenersForEvent.forEach(listener => {\n                isProcessed = listener(...args) || isProcessed;\n            });\n        }\n\n        if (!isProcessed) {\n            this._unprocessedMessages.add(args);\n        }\n\n        return isProcessed;\n    }\n\n    /**\n     * Adds the listener function to the listeners collection for the event\n     * named eventName.\n     *\n     * @param {string} eventName -  The name of the event.\n     * @param {Function} listener - The listener that will be added.\n     * @returns {Transport} References to the instance of Transport class, so\n     * that calls can be chained.\n     */\n    on(eventName, listener) {\n        let listenersForEvent = this._listeners.get(eventName);\n\n        if (!listenersForEvent) {\n            listenersForEvent = new Set();\n            this._listeners.set(eventName, listenersForEvent);\n        }\n\n        listenersForEvent.add(listener);\n\n        this._unprocessedMessages.forEach(args => {\n            if (listener(...args)) {\n                this._unprocessedMessages.delete(args);\n            }\n        });\n\n        return this;\n    }\n\n    /**\n     * Removes all listeners, or those of the specified eventName.\n     *\n     * @param {string} [eventName] - The name of the event. If this parameter is\n     * not specified all listeners will be removed.\n     * @returns {Transport} References to the instance of Transport class, so\n     * that calls can be chained.\n     */\n    removeAllListeners(eventName) {\n        if (eventName) {\n            this._listeners.delete(eventName);\n        } else {\n            this._listeners.clear();\n        }\n\n        return this;\n    }\n\n    /**\n     * Removes the listener function from the listeners collection for the event\n     * named eventName.\n     *\n     * @param {string} eventName -  The name of the event.\n     * @param {Function} listener - The listener that will be removed.\n     * @returns {Transport} References to the instance of Transport class, so\n     * that calls can be chained.\n     */\n    removeListener(eventName, listener) {\n        const listenersForEvent = this._listeners.get(eventName);\n\n        if (listenersForEvent) {\n            listenersForEvent.delete(listener);\n        }\n\n        return this;\n    }\n\n    /**\n     * Sends the passed event.\n     *\n     * @param {Object} event - The event to be sent.\n     * @returns {void}\n     */\n    sendEvent(event = {}) {\n        if (this._backend) {\n            this._backend.send({\n                type: MESSAGE_TYPE_EVENT,\n                data: event\n            });\n        }\n    }\n\n    /**\n     * Sending request.\n     *\n     * @param {Object} request - The request to be sent.\n     * @returns {Promise}\n     */\n    sendRequest(request) {\n        if (!this._backend) {\n            return Promise.reject(new Error('No transport backend defined!'));\n        }\n\n        this._requestID++;\n\n        const id = this._requestID;\n\n        return new Promise((resolve, reject) => {\n            this._responseHandlers.set(id, ({ error, result }) => {\n                if (typeof result !== 'undefined') {\n                    resolve(result);\n\n                // eslint-disable-next-line no-negated-condition\n                } else if (typeof error !== 'undefined') {\n                    reject(error);\n                } else { // no response\n                    reject(new Error('Unexpected response format!'));\n                }\n            });\n\n            this._backend.send({\n                type: MESSAGE_TYPE_REQUEST,\n                data: request,\n                id\n            });\n        });\n    }\n\n    /**\n     * Changes the current backend transport.\n     *\n     * @param {Object} backend - The new transport backend that will be used.\n     * @returns {void}\n     */\n    setBackend(backend) {\n        this._disposeBackend();\n\n        this._backend = backend;\n        this._backend.setReceiveCallback(this._onMessageReceived.bind(this));\n    }\n}\n", "/**\n * A helper function that behaves similar to Object.assign, but only reassigns a\n * property in target if it's defined in source.\n *\n * @param {Object} target - The target object to assign the values into.\n * @param {Object} source - The source object.\n * @returns {Object}\n */\nexport function assignIfDefined(target, source) {\n    const to = Object(target);\n    for (const nextKey in source) {\n        if (source.hasOwnProperty(nextKey)) {\n            const value = source[nextKey];\n            if (typeof value !== 'undefined') {\n                to[nextKey] = value;\n            }\n        }\n    }\n    return to;\n}\n/**\n * Creates a deferred object.\n *\n * @returns {{promise, resolve, reject}}\n */\nexport function createDeferred() {\n    const deferred = {};\n    deferred.promise = new Promise((resolve, reject) => {\n        deferred.resolve = resolve;\n        deferred.reject = reject;\n    });\n    return deferred;\n}\nconst MATCH_OPERATOR_REGEXP = /[|\\\\{}()[\\]^$+*?.-]/g;\n/**\n * Escape RegExp special characters.\n *\n * Based on https://github.com/sindresorhus/escape-string-regexp.\n *\n * @param {string} s - The regexp string to escape.\n * @returns {string}\n */\nexport function escapeRegexp(s) {\n    if (typeof s !== 'string') {\n        throw new TypeError('Expected a string');\n    }\n    return s.replace(MATCH_OPERATOR_REGEXP, '\\\\$&');\n}\n/**\n * Returns the base URL of the app.\n *\n * @param {Object} w - Window object to use instead of the built in one.\n * @returns {string}\n */\nexport function getBaseUrl(w = window) {\n    const doc = w.document;\n    const base = doc.querySelector('base');\n    if (base?.href) {\n        return base.href;\n    }\n    const { protocol, host } = w.location;\n    return `${protocol}//${host}`;\n}\n/**\n * Returns the namespace for all global variables, functions, etc that we need.\n *\n * @returns {Object} The namespace.\n *\n * NOTE: After React-ifying everything this should be the only global.\n */\nexport function getJitsiMeetGlobalNS() {\n    if (!window.JitsiMeetJS) {\n        window.JitsiMeetJS = {};\n    }\n    if (!window.JitsiMeetJS.app) {\n        window.JitsiMeetJS.app = {};\n    }\n    return window.JitsiMeetJS.app;\n}\n/**\n * Prints the error and reports it to the global error handler.\n *\n * @param {Error} e - The error object.\n * @param {string} msg - A custom message to print in addition to the error.\n * @returns {void}\n */\nexport function reportError(e, msg = '') {\n    console.error(msg, e);\n    window.onerror?.(msg, undefined, undefined, undefined, e);\n}\n/**\n * Adds alpha to a color css string.\n *\n * @param {string} color - The color string either in rgb... Or #... Format.\n * @param {number} opacity -The opacity(alpha) to apply to the color. Can take a value between 0 and 1, including.\n * @returns {string} - The color with applied alpha.\n */\nexport function setColorAlpha(color, opacity) {\n    if (!color) {\n        return `rgba(0, 0, 0, ${opacity})`;\n    }\n    let b, g, r;\n    try {\n        if (color.startsWith('rgb')) {\n            [r, g, b] = color.split('(')[1].split(')')[0].split(',').map(c => c.trim());\n        }\n        else if (color.startsWith('#')) {\n            if (color.length === 4) {\n                [r, g, b] = parseShorthandColor(color);\n            }\n            else {\n                r = parseInt(color.substring(1, 3), 16);\n                g = parseInt(color.substring(3, 5), 16);\n                b = parseInt(color.substring(5, 7), 16);\n            }\n        }\n        else {\n            return color;\n        }\n        return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n    }\n    catch {\n        return color;\n    }\n}\n/**\n * Gets the hexa rgb values for a shorthand css color.\n *\n * @param {string} color -\n * @returns {Array<number>} - Array containing parsed r, g, b values of the color.\n */\nfunction parseShorthandColor(color) {\n    let b, g, r;\n    r = color.substring(1, 2);\n    r += r;\n    r = parseInt(r, 16);\n    g = color.substring(2, 3);\n    g += g;\n    g = parseInt(g, 16);\n    b = color.substring(3, 4);\n    b += b;\n    b = parseInt(b, 16);\n    return [r, g, b];\n}\n/**\n * Sorts an object by a sort function, same functionality as array.sort().\n *\n * @param {Object} object - The data object.\n * @param {Function} callback - The sort function.\n * @returns {void}\n */\nexport function objectSort(object, callback) {\n    return Object.entries(object)\n        .sort(([, a], [, b]) => callback(a, b))\n        .reduce((row, [key, value]) => {\n        return { ...row,\n            [key]: value };\n    }, {});\n}\n", "// XXX The function parseURLParams is exported by the feature base/util (as\n// defined in the terminology of react/). However, this file is (very likely)\n// bundled in external_api in addition to app.bundle and, consequently, it is\n// best to import as little as possible here (rather than the whole feature\n// base/util) in order to minimize the amount of source code bundled into\n// multiple bundles.\nimport { parseURLParams } from '../../react/features/base/util/parseURLParams';\n\n/**\n * JitsiMeetExternalAPI id - unique for a webpage.\n */\nexport const API_ID = parseURLParams(window.location).jitsi_meet_external_api_id;\n\n/**\n * The payload name for the datachannel/endpoint text message event.\n */\nexport const ENDPOINT_TEXT_MESSAGE_NAME = 'endpoint-text-message';\n\n/**\n * The min value that can be set for the assumed bandwidth.\n * Setting it to this value means not assuming any bandwidth,\n * but rather allowing the estimations to take place.\n */\nexport const ASSUMED_BANDWIDTH_BPS = -1;\n", "// FIXME: change to '../API' when we update to webpack2. If we do this now all\n// files from API modules will be included in external_api.js.\nimport { PostMessageTransportBackend, Transport } from '@jitsi/js-utils/transport';\n\nimport { getJitsiMeetGlobalNS } from '../../react/features/base/util/helpers';\nimport { API_ID } from '../API/constants';\n\n\nexport {\n    PostMessageTransportBackend,\n    Transport\n};\n\n/**\n * Option for the default low level transport.\n *\n * @type {Object}\n */\nconst postisOptions = {};\n\nif (typeof API_ID === 'number') {\n    postisOptions.scope = `jitsi_meet_external_api_${API_ID}`;\n}\n\n/**\n * The instance of Transport class that will be used by Jitsi Meet.\n *\n * @type {Transport}\n */\nlet transport;\n\n/**\n * Returns the instance of Transport class that will be used by Jitsi Meet.\n *\n * @returns {Transport}\n */\nexport function getJitsiMeetTransport() {\n    if (!transport) {\n        transport = new Transport({ backend: new PostMessageTransportBackend({ postisOptions }) });\n    }\n\n    return transport;\n}\n\n/**\n * Sets the transport to passed transport.\n *\n * @param {Object} externalTransportBackend - The new transport.\n * @returns {void}\n */\ngetJitsiMeetGlobalNS().setExternalTransportBackend = externalTransportBackend =>\n    transport.setBackend(externalTransportBackend);\n", "import Logger from '@jitsi/logger';\n\nconst logger = Logger.getLogger(__filename);\n\n/**\n * Returns Promise that resolves with result an list of available devices.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @returns {Promise}\n */\nexport function getAvailableDevices(transport) {\n    return transport.sendRequest({\n        type: 'devices',\n        name: 'getAvailableDevices'\n    }).catch(e => {\n        logger.error(e);\n\n        return {};\n    });\n}\n\n/**\n * Returns Promise that resolves with current selected devices.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @returns {Promise}\n */\nexport function getCurrentDevices(transport) {\n    return transport.sendRequest({\n        type: 'devices',\n        name: 'getCurrentDevices'\n    }).catch(e => {\n        logger.error(e);\n\n        return {};\n    });\n}\n\n/**\n * Returns Promise that resolves with true if the device change is available\n * and with false if not.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @param {string} [deviceType] - Values - 'output', 'input' or undefined.\n * Default - 'input'.\n * @returns {Promise}\n */\nexport function isDeviceChangeAvailable(transport, deviceType) {\n    return transport.sendRequest({\n        deviceType,\n        type: 'devices',\n        name: 'isDeviceChangeAvailable'\n    });\n}\n\n/**\n * Returns Promise that resolves with true if the device list is available\n * and with false if not.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @returns {Promise}\n */\nexport function isDeviceListAvailable(transport) {\n    return transport.sendRequest({\n        type: 'devices',\n        name: 'isDeviceListAvailable'\n    });\n}\n\n/**\n * Returns Promise that resolves with true if multiple audio input is supported\n * and with false if not.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @returns {Promise}\n */\nexport function isMultipleAudioInputSupported(transport) {\n    return transport.sendRequest({\n        type: 'devices',\n        name: 'isMultipleAudioInputSupported'\n    });\n}\n\n/**\n * Sets the audio input device to the one with the label or id that is passed.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @param {string} label - The label of the new device.\n * @param {string} id - The id of the new device.\n * @returns {Promise}\n */\nexport function setAudioInputDevice(transport, label, id) {\n    return _setDevice(transport, {\n        id,\n        kind: 'audioinput',\n        label\n    });\n}\n\n/**\n * Sets the audio output device to the one with the label or id that is passed.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @param {string} label - The label of the new device.\n * @param {string} id - The id of the new device.\n * @returns {Promise}\n */\nexport function setAudioOutputDevice(transport, label, id) {\n    return _setDevice(transport, {\n        id,\n        kind: 'audiooutput',\n        label\n    });\n}\n\n/**\n * Sets the currently used device to the one that is passed.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @param {Object} device - The new device to be used.\n * @returns {Promise}\n */\nfunction _setDevice(transport, device) {\n    return transport.sendRequest({\n        type: 'devices',\n        name: 'setDevice',\n        device\n    });\n}\n\n/**\n * Sets the video input device to the one with the label or id that is passed.\n *\n * @param {Transport} transport - The @code{Transport} instance responsible for\n * the external communication.\n * @param {string} label - The label of the new device.\n * @param {string} id - The id of the new device.\n * @returns {Promise}\n */\nexport function setVideoInputDevice(transport, label, id) {\n    return _setDevice(transport, {\n        id,\n        kind: 'videoinput',\n        label\n    });\n}\n", "import { jitsiLocalStorage } from '@jitsi/js-utils/jitsi-local-storage';\nimport EventEmitter from 'events';\n\nimport { urlObjectToString } from '../../../react/features/base/util/uri';\nimport {\n    PostMessageTransportBackend,\n    Transport\n} from '../../transport';\n\nimport {\n    getAvailableDevices,\n    getCurrentDevices,\n    isDeviceChangeAvailable,\n    isDeviceListAvailable,\n    isMultipleAudioInputSupported,\n    setAudioInputDevice,\n    setAudioOutputDevice,\n    setVideoInputDevice\n} from './functions';\n\nconst ALWAYS_ON_TOP_FILENAMES = [\n    'css/all.css', 'libs/alwaysontop.min.js'\n];\n\n/**\n * Maps the names of the commands expected by the API with the name of the\n * commands expected by jitsi-meet.\n */\nconst commands = {\n    addBreakoutRoom: 'add-breakout-room',\n    answerKnockingParticipant: 'answer-knocking-participant',\n    approveVideo: 'approve-video',\n    askToUnmute: 'ask-to-unmute',\n    autoAssignToBreakoutRooms: 'auto-assign-to-breakout-rooms',\n    avatarUrl: 'avatar-url',\n    cancelPrivateChat: 'cancel-private-chat',\n    closeBreakoutRoom: 'close-breakout-room',\n    displayName: 'display-name',\n    endConference: 'end-conference',\n    email: 'email',\n    grantModerator: 'grant-moderator',\n    hangup: 'video-hangup',\n    hideNotification: 'hide-notification',\n    initiatePrivateChat: 'initiate-private-chat',\n    joinBreakoutRoom: 'join-breakout-room',\n    localSubject: 'local-subject',\n    kickParticipant: 'kick-participant',\n    muteEveryone: 'mute-everyone',\n    overwriteConfig: 'overwrite-config',\n    overwriteNames: 'overwrite-names',\n    password: 'password',\n    pinParticipant: 'pin-participant',\n    rejectParticipant: 'reject-participant',\n    removeBreakoutRoom: 'remove-breakout-room',\n    resizeFilmStrip: 'resize-film-strip',\n    resizeLargeVideo: 'resize-large-video',\n    sendChatMessage: 'send-chat-message',\n    sendEndpointTextMessage: 'send-endpoint-text-message',\n    sendParticipantToRoom: 'send-participant-to-room',\n    sendTones: 'send-tones',\n    setAssumedBandwidthBps: 'set-assumed-bandwidth-bps',\n    setFollowMe: 'set-follow-me',\n    setLargeVideoParticipant: 'set-large-video-participant',\n    setMediaEncryptionKey: 'set-media-encryption-key',\n    setNoiseSuppressionEnabled: 'set-noise-suppression-enabled',\n    setParticipantVolume: 'set-participant-volume',\n    setSubtitles: 'set-subtitles',\n    setTileView: 'set-tile-view',\n    setVideoQuality: 'set-video-quality',\n    showNotification: 'show-notification',\n    startRecording: 'start-recording',\n    startShareVideo: 'start-share-video',\n    stopRecording: 'stop-recording',\n    stopShareVideo: 'stop-share-video',\n    subject: 'subject',\n    submitFeedback: 'submit-feedback',\n    toggleAudio: 'toggle-audio',\n    toggleCamera: 'toggle-camera',\n    toggleCameraMirror: 'toggle-camera-mirror',\n    toggleChat: 'toggle-chat',\n    toggleE2EE: 'toggle-e2ee',\n    toggleFilmStrip: 'toggle-film-strip',\n    toggleLobby: 'toggle-lobby',\n    toggleModeration: 'toggle-moderation',\n    toggleNoiseSuppression: 'toggle-noise-suppression',\n    toggleParticipantsPane: 'toggle-participants-pane',\n    toggleRaiseHand: 'toggle-raise-hand',\n    toggleShareScreen: 'toggle-share-screen',\n    toggleSubtitles: 'toggle-subtitles',\n    toggleTileView: 'toggle-tile-view',\n    toggleVirtualBackgroundDialog: 'toggle-virtual-background',\n    toggleVideo: 'toggle-video',\n    toggleWhiteboard: 'toggle-whiteboard'\n};\n\n/**\n * Maps the names of the events expected by the API with the name of the\n * events expected by jitsi-meet.\n */\nconst events = {\n    'avatar-changed': 'avatarChanged',\n    'audio-availability-changed': 'audioAvailabilityChanged',\n    'audio-mute-status-changed': 'audioMuteStatusChanged',\n    'audio-or-video-sharing-toggled': 'audioOrVideoSharingToggled',\n    'breakout-rooms-updated': 'breakoutRoomsUpdated',\n    'browser-support': 'browserSupport',\n    'camera-error': 'cameraError',\n    'chat-updated': 'chatUpdated',\n    'content-sharing-participants-changed': 'contentSharingParticipantsChanged',\n    'data-channel-closed': 'dataChannelClosed',\n    'data-channel-opened': 'dataChannelOpened',\n    'device-list-changed': 'deviceListChanged',\n    'display-name-change': 'displayNameChange',\n    'email-change': 'emailChange',\n    'error-occurred': 'errorOccurred',\n    'endpoint-text-message-received': 'endpointTextMessageReceived',\n    'face-landmark-detected': 'faceLandmarkDetected',\n    'feedback-submitted': 'feedbackSubmitted',\n    'feedback-prompt-displayed': 'feedbackPromptDisplayed',\n    'filmstrip-display-changed': 'filmstripDisplayChanged',\n    'incoming-message': 'incomingMessage',\n    'knocking-participant': 'knockingParticipant',\n    'log': 'log',\n    'mic-error': 'micError',\n    'moderation-participant-approved': 'moderationParticipantApproved',\n    'moderation-participant-rejected': 'moderationParticipantRejected',\n    'moderation-status-changed': 'moderationStatusChanged',\n    'mouse-enter': 'mouseEnter',\n    'mouse-leave': 'mouseLeave',\n    'mouse-move': 'mouseMove',\n    'notification-triggered': 'notificationTriggered',\n    'outgoing-message': 'outgoingMessage',\n    'p2p-status-changed': 'p2pStatusChanged',\n    'participant-joined': 'participantJoined',\n    'participant-kicked-out': 'participantKickedOut',\n    'participant-left': 'participantLeft',\n    'participant-role-changed': 'participantRoleChanged',\n    'participants-pane-toggled': 'participantsPaneToggled',\n    'password-required': 'passwordRequired',\n    'peer-connection-failure': 'peerConnectionFailure',\n    'prejoin-screen-loaded': 'prejoinScreenLoaded',\n    'proxy-connection-event': 'proxyConnectionEvent',\n    'raise-hand-updated': 'raiseHandUpdated',\n    'recording-link-available': 'recordingLinkAvailable',\n    'recording-status-changed': 'recordingStatusChanged',\n    'participant-menu-button-clicked': 'participantMenuButtonClick',\n    'video-ready-to-close': 'readyToClose',\n    'video-conference-joined': 'videoConferenceJoined',\n    'video-conference-left': 'videoConferenceLeft',\n    'video-availability-changed': 'videoAvailabilityChanged',\n    'video-mute-status-changed': 'videoMuteStatusChanged',\n    'video-quality-changed': 'videoQualityChanged',\n    'screen-sharing-status-changed': 'screenSharingStatusChanged',\n    'dominant-speaker-changed': 'dominantSpeakerChanged',\n    'subject-change': 'subjectChange',\n    'suspend-detected': 'suspendDetected',\n    'tile-view-changed': 'tileViewChanged',\n    'toolbar-button-clicked': 'toolbarButtonClicked',\n    'whiteboard-status-changed': 'whiteboardStatusChanged'\n};\n\n/**\n * Last id of api object.\n *\n * @type {number}\n */\nlet id = 0;\n\n/**\n * Adds given number to the numberOfParticipants property of given APIInstance.\n *\n * @param {JitsiMeetExternalAPI} APIInstance - The instance of the API.\n * @param {int} number - The number of participants to be added to\n * numberOfParticipants property (this parameter can be negative number if the\n * numberOfParticipants should be decreased).\n * @returns {void}\n */\nfunction changeParticipantNumber(APIInstance, number) {\n    APIInstance._numberOfParticipants += number;\n}\n\n/**\n * Generates the URL for the iframe.\n *\n * @param {string} domain - The domain name of the server that hosts the\n * conference.\n * @param {string} [options] - Another optional parameters.\n * @param {Object} [options.configOverwrite] - Object containing configuration\n * options defined in config.js to be overridden.\n * @param {Object} [options.interfaceConfigOverwrite] - Object containing\n * configuration options defined in interface_config.js to be overridden.\n * @param {string} [options.jwt] - The JWT token if needed by jitsi-meet for\n * authentication.\n * @param {string} [options.lang] - The meeting's default language.\n * @param {string} [options.roomName] - The name of the room to join.\n * @returns {string} The URL.\n */\nfunction generateURL(domain, options = {}) {\n    return urlObjectToString({\n        ...options,\n        url: `https://${domain}/#jitsi_meet_external_api_id=${id}`\n    });\n}\n\n/**\n * Parses the arguments passed to the constructor. If the old format is used\n * the function translates the arguments to the new format.\n *\n * @param {Array} args - The arguments to be parsed.\n * @returns {Object} JS object with properties.\n */\nfunction parseArguments(args) {\n    if (!args.length) {\n        return {};\n    }\n\n    const firstArg = args[0];\n\n    switch (typeof firstArg) {\n    case 'string': // old arguments format\n    case 'undefined': {\n        // Not sure which format but we are trying to parse the old\n        // format because if the new format is used everything will be undefined\n        // anyway.\n        const [\n            roomName,\n            width,\n            height,\n            parentNode,\n            configOverwrite,\n            interfaceConfigOverwrite,\n            jwt,\n            onload,\n            lang\n        ] = args;\n\n        return {\n            roomName,\n            width,\n            height,\n            parentNode,\n            configOverwrite,\n            interfaceConfigOverwrite,\n            jwt,\n            onload,\n            lang\n        };\n    }\n    case 'object': // new arguments format\n        return args[0];\n    default:\n        throw new Error('Can\\'t parse the arguments!');\n    }\n}\n\n/**\n * Compute valid values for height and width. If a number is specified it's\n * treated as pixel units. If the value is expressed in px, em, pt or\n * percentage, it's used as is.\n *\n * @param {any} value - The value to be parsed.\n * @returns {string|undefined} The parsed value that can be used for setting\n * sizes through the style property. If invalid value is passed the method\n * returns undefined.\n */\nfunction parseSizeParam(value) {\n    let parsedValue;\n\n    // This regex parses values of the form 100px, 100em, 100pt or 100%.\n    // Values like 100 or 100px are handled outside of the regex, and\n    // invalid values will be ignored and the minimum will be used.\n    const re = /([0-9]*\\.?[0-9]+)(em|pt|px|%)$/;\n\n    if (typeof value === 'string' && String(value).match(re) !== null) {\n        parsedValue = value;\n    } else if (typeof value === 'number') {\n        parsedValue = `${value}px`;\n    }\n\n    return parsedValue;\n}\n\n\n/**\n * The IFrame API interface class.\n */\nexport default class JitsiMeetExternalAPI extends EventEmitter {\n    /**\n     * Constructs new API instance. Creates iframe and loads Jitsi Meet in it.\n     *\n     * @param {string} domain - The domain name of the server that hosts the\n     * conference.\n     * @param {Object} [options] - Optional arguments.\n     * @param {string} [options.roomName] - The name of the room to join.\n     * @param {number|string} [options.width] - Width of the iframe. Check\n     * parseSizeParam for format details.\n     * @param {number|string} [options.height] - Height of the iframe. Check\n     * parseSizeParam for format details.\n     * @param {DOMElement} [options.parentNode] - The node that will contain the\n     * iframe.\n     * @param {Object} [options.configOverwrite] - Object containing\n     * configuration options defined in config.js to be overridden.\n     * @param {Object} [options.interfaceConfigOverwrite] - Object containing\n     * configuration options defined in interface_config.js to be overridden.\n     * @param {string} [options.jwt] - The JWT token if needed by jitsi-meet for\n     * authentication.\n     * @param {string} [options.lang] - The meeting's default language.\n     * @param {string} [options.onload] - The onload function that will listen\n     * for iframe onload event.\n     * @param {Array<Object>} [options.invitees] - Array of objects containing\n     * information about new participants that will be invited in the call.\n     * @param {Array<Object>} [options.devices] - Array of objects containing\n     * information about the initial devices that will be used in the call.\n     * @param {Object} [options.userInfo] - Object containing information about\n     * the participant opening the meeting.\n     * @param {string}  [options.e2eeKey] - The key used for End-to-End encryption.\n     * THIS IS EXPERIMENTAL.\n     * @param {string}  [options.release] - The key used for specifying release if enabled on the backend.\n     * @param {string} [options.sandbox] - Sandbox directive for the created iframe, if desired.\n     */\n    constructor(domain, ...args) {\n        super();\n        const {\n            roomName = '',\n            width = '100%',\n            height = '100%',\n            parentNode = document.body,\n            configOverwrite = {},\n            interfaceConfigOverwrite = {},\n            jwt = undefined,\n            lang = undefined,\n            onload = undefined,\n            invitees,\n            devices,\n            userInfo,\n            e2eeKey,\n            release,\n            sandbox = ''\n        } = parseArguments(args);\n        const localStorageContent = jitsiLocalStorage.getItem('jitsiLocalStorage');\n\n        this._parentNode = parentNode;\n        this._url = generateURL(domain, {\n            configOverwrite,\n            interfaceConfigOverwrite,\n            jwt,\n            lang,\n            roomName,\n            devices,\n            userInfo,\n            appData: {\n                localStorageContent\n            },\n            release\n        });\n        this._createIFrame(height, width, onload, sandbox);\n        this._transport = new Transport({\n            backend: new PostMessageTransportBackend({\n                postisOptions: {\n                    allowedOrigin: new URL(this._url).origin,\n                    scope: `jitsi_meet_external_api_${id}`,\n                    window: this._frame.contentWindow\n                }\n            })\n        });\n        if (Array.isArray(invitees) && invitees.length > 0) {\n            this.invite(invitees);\n        }\n        this._tmpE2EEKey = e2eeKey;\n        this._isLargeVideoVisible = false;\n        this._isPrejoinVideoVisible = false;\n        this._numberOfParticipants = 0;\n        this._participants = {};\n        this._myUserID = undefined;\n        this._onStageParticipant = undefined;\n        this._setupListeners();\n        id++;\n    }\n\n    /**\n     * Creates the iframe element.\n     *\n     * @param {number|string} height - The height of the iframe. Check\n     * parseSizeParam for format details.\n     * @param {number|string} width - The with of the iframe. Check\n     * parseSizeParam for format details.\n     * @param {Function} onload - The function that will listen\n     * for onload event.\n     * @param {string} sandbox - Sandbox directive for the created iframe, if desired.\n     * @returns {void}\n     *\n     * @private\n     */\n    _createIFrame(height, width, onload, sandbox) {\n        const frameName = `jitsiConferenceFrame${id}`;\n\n        this._frame = document.createElement('iframe');\n        this._frame.allow = 'camera; microphone; display-capture; autoplay; clipboard-write; hid';\n        this._frame.name = frameName;\n        this._frame.id = frameName;\n        this._setSize(height, width);\n        this._frame.setAttribute('allowFullScreen', 'true');\n        this._frame.style.border = 0;\n\n        if (sandbox) {\n            this._frame.sandbox = sandbox;\n        }\n\n        if (onload) {\n            // waits for iframe resources to load\n            // and fires event when it is done\n            this._frame.onload = onload;\n        }\n        this._frame.src = this._url;\n\n        this._frame = this._parentNode.appendChild(this._frame);\n    }\n\n    /**\n     * Returns arrays with the all resources for the always on top feature.\n     *\n     * @returns {Array<string>}\n     */\n    _getAlwaysOnTopResources() {\n        const iframeWindow = this._frame.contentWindow;\n        const iframeDocument = iframeWindow.document;\n        let baseURL = '';\n        const base = iframeDocument.querySelector('base');\n\n        if (base && base.href) {\n            baseURL = base.href;\n        } else {\n            const { protocol, host } = iframeWindow.location;\n\n            baseURL = `${protocol}//${host}`;\n        }\n\n        return ALWAYS_ON_TOP_FILENAMES.map(\n            filename => new URL(filename, baseURL).href\n        );\n    }\n\n    /**\n     * Returns the formatted display name of a participant.\n     *\n     * @param {string} participantId - The id of the participant.\n     * @returns {string} The formatted display name.\n     */\n    _getFormattedDisplayName(participantId) {\n        const { formattedDisplayName }\n            = this._participants[participantId] || {};\n\n        return formattedDisplayName;\n    }\n\n    /**\n     * Returns the id of the on stage participant.\n     *\n     * @returns {string} - The id of the on stage participant.\n     */\n    _getOnStageParticipant() {\n        return this._onStageParticipant;\n    }\n\n\n    /**\n     * Getter for the large video element in Jitsi Meet.\n     *\n     * @returns {HTMLElement|undefined} - The large video.\n     */\n    _getLargeVideo() {\n        const iframe = this.getIFrame();\n\n        if (!this._isLargeVideoVisible\n                || !iframe\n                || !iframe.contentWindow\n                || !iframe.contentWindow.document) {\n            return;\n        }\n\n        return iframe.contentWindow.document.getElementById('largeVideo');\n    }\n\n    /**\n     * Getter for the prejoin video element in Jitsi Meet.\n     *\n     * @returns {HTMLElement|undefined} - The prejoin video.\n     */\n    _getPrejoinVideo() {\n        const iframe = this.getIFrame();\n\n        if (!this._isPrejoinVideoVisible\n                || !iframe\n                || !iframe.contentWindow\n                || !iframe.contentWindow.document) {\n            return;\n        }\n\n        return iframe.contentWindow.document.getElementById('prejoinVideo');\n    }\n\n    /**\n     * Getter for participant specific video element in Jitsi Meet.\n     *\n     * @param {string|undefined} participantId - Id of participant to return the video for.\n     *\n     * @returns {HTMLElement|undefined} - The requested video. Will return the local video\n     * by default if participantId is undefined.\n     */\n    _getParticipantVideo(participantId) {\n        const iframe = this.getIFrame();\n\n        if (!iframe\n                || !iframe.contentWindow\n                || !iframe.contentWindow.document) {\n            return;\n        }\n\n        if (typeof participantId === 'undefined' || participantId === this._myUserID) {\n            return iframe.contentWindow.document.getElementById('localVideo_container');\n        }\n\n        return iframe.contentWindow.document.querySelector(`#participant_${participantId} video`);\n    }\n\n    /**\n     * Sets the size of the iframe element.\n     *\n     * @param {number|string} height - The height of the iframe.\n     * @param {number|string} width - The with of the iframe.\n     * @returns {void}\n     *\n     * @private\n     */\n    _setSize(height, width) {\n        const parsedHeight = parseSizeParam(height);\n        const parsedWidth = parseSizeParam(width);\n\n        if (parsedHeight !== undefined) {\n            this._height = height;\n            this._frame.style.height = parsedHeight;\n        }\n\n        if (parsedWidth !== undefined) {\n            this._width = width;\n            this._frame.style.width = parsedWidth;\n        }\n    }\n\n    /**\n     * Setups listeners that are used internally for JitsiMeetExternalAPI.\n     *\n     * @returns {void}\n     *\n     * @private\n     */\n    _setupListeners() {\n        this._transport.on('event', ({ name, ...data }) => {\n            const userID = data.id;\n\n            switch (name) {\n            case 'video-conference-joined': {\n                if (typeof this._tmpE2EEKey !== 'undefined') {\n\n                    const hexToBytes = hex => {\n                        const bytes = [];\n\n                        for (let c = 0; c < hex.length; c += 2) {\n                            bytes.push(parseInt(hex.substring(c, c + 2), 16));\n                        }\n\n                        return bytes;\n                    };\n\n                    this.executeCommand('setMediaEncryptionKey', JSON.stringify({\n                        exportedKey: hexToBytes(this._tmpE2EEKey),\n                        index: 0\n                    }));\n\n                    this._tmpE2EEKey = undefined;\n                }\n\n                this._myUserID = userID;\n                this._participants[userID] = {\n                    email: data.email,\n                    avatarURL: data.avatarURL\n                };\n            }\n\n            // eslint-disable-next-line no-fallthrough\n            case 'participant-joined': {\n                this._participants[userID] = this._participants[userID] || {};\n                this._participants[userID].displayName = data.displayName;\n                this._participants[userID].formattedDisplayName\n                    = data.formattedDisplayName;\n                changeParticipantNumber(this, 1);\n                break;\n            }\n            case 'participant-left':\n                changeParticipantNumber(this, -1);\n                delete this._participants[userID];\n                break;\n            case 'display-name-change': {\n                const user = this._participants[userID];\n\n                if (user) {\n                    user.displayName = data.displayname;\n                    user.formattedDisplayName = data.formattedDisplayName;\n                }\n                break;\n            }\n            case 'email-change': {\n                const user = this._participants[userID];\n\n                if (user) {\n                    user.email = data.email;\n                }\n                break;\n            }\n            case 'avatar-changed': {\n                const user = this._participants[userID];\n\n                if (user) {\n                    user.avatarURL = data.avatarURL;\n                }\n                break;\n            }\n            case 'on-stage-participant-changed':\n                this._onStageParticipant = userID;\n                this.emit('largeVideoChanged');\n                break;\n            case 'large-video-visibility-changed':\n                this._isLargeVideoVisible = data.isVisible;\n                this.emit('largeVideoChanged');\n                break;\n            case 'prejoin-screen-loaded':\n                this._participants[userID] = {\n                    displayName: data.displayName,\n                    formattedDisplayName: data.formattedDisplayName\n                };\n                break;\n            case 'on-prejoin-video-changed':\n                this._isPrejoinVideoVisible = data.isVisible;\n                this.emit('prejoinVideoChanged');\n                break;\n            case 'video-conference-left':\n                changeParticipantNumber(this, -1);\n                delete this._participants[this._myUserID];\n                break;\n            case 'video-quality-changed':\n                this._videoQuality = data.videoQuality;\n                break;\n            case 'breakout-rooms-updated':\n                this.updateNumberOfParticipants(data.rooms);\n                break;\n            case 'local-storage-changed':\n                jitsiLocalStorage.setItem('jitsiLocalStorage', data.localStorageContent);\n\n                // Since this is internal event we don't need to emit it to the consumer of the API.\n                return true;\n            }\n\n            const eventName = events[name];\n\n            if (eventName) {\n                this.emit(eventName, data);\n\n                return true;\n            }\n\n            return false;\n        });\n    }\n\n    /**\n     * Update number of participants based on all rooms.\n     *\n     * @param {Object} rooms - Rooms available rooms in the conference.\n     * @returns {void}\n     */\n    updateNumberOfParticipants(rooms) {\n        if (!rooms || !Object.keys(rooms).length) {\n            return;\n        }\n\n        const allParticipants = Object.keys(rooms).reduce((prev, roomItemKey) => {\n            if (rooms[roomItemKey]?.participants) {\n                return Object.keys(rooms[roomItemKey].participants).length + prev;\n            }\n\n            return prev;\n        }, 0);\n\n        this._numberOfParticipants = allParticipants;\n    }\n\n    /**\n     * Returns the rooms info in the conference.\n     *\n     * @returns {Object} Rooms info.\n     */\n    async getRoomsInfo() {\n        return this._transport.sendRequest({\n            name: 'rooms-info'\n        });\n    }\n\n    /**\n     * Returns whether the conference is P2P.\n     *\n     * @returns {Promise}\n     */\n    isP2pActive() {\n        return this._transport.sendRequest({\n            name: 'get-p2p-status'\n        });\n    }\n\n    /**\n     * Adds event listener to Meet Jitsi.\n     *\n     * @param {string} event - The name of the event.\n     * @param {Function} listener - The listener.\n     * @returns {void}\n     *\n     * @deprecated\n     * NOTE: This method is not removed for backward comatability purposes.\n     */\n    addEventListener(event, listener) {\n        this.on(event, listener);\n    }\n\n    /**\n     * Adds event listeners to Meet Jitsi.\n     *\n     * @param {Object} listeners - The object key should be the name of\n     * the event and value - the listener.\n     * Currently we support the following\n     * events:\n     * {@code log} - receives event notifications whenever information has\n     * been logged and has a log level specified within {@code config.apiLogLevels}.\n     * The listener will receive object with the following structure:\n     * {{\n     * logLevel: the message log level\n     * arguments: an array of strings that compose the actual log message\n     * }}\n     * {@code chatUpdated} - receives event notifications about chat state being\n     * updated. The listener will receive object with the following structure:\n     * {{\n     *  'unreadCount': unreadCounter, // the unread message(s) counter,\n     *  'isOpen': isOpen, // whether the chat panel is open or not\n     * }}\n     * {@code incomingMessage} - receives event notifications about incoming\n     * messages. The listener will receive object with the following structure:\n     * {{\n     *  'from': from,//JID of the user that sent the message\n     *  'nick': nick,//the nickname of the user that sent the message\n     *  'message': txt//the text of the message\n     * }}\n     * {@code outgoingMessage} - receives event notifications about outgoing\n     * messages. The listener will receive object with the following structure:\n     * {{\n     *  'message': txt//the text of the message\n     * }}\n     * {@code displayNameChanged} - receives event notifications about display\n     * name change. The listener will receive object with the following\n     * structure:\n     * {{\n     * jid: jid,//the JID of the participant that changed his display name\n     * displayname: displayName //the new display name\n     * }}\n     * {@code participantJoined} - receives event notifications about new\n     * participant.\n     * The listener will receive object with the following structure:\n     * {{\n     * jid: jid //the jid of the participant\n     * }}\n     * {@code participantLeft} - receives event notifications about the\n     * participant that left the room.\n     * The listener will receive object with the following structure:\n     * {{\n     * jid: jid //the jid of the participant\n     * }}\n     * {@code videoConferenceJoined} - receives event notifications about the\n     * local user has successfully joined the video conference.\n     * The listener will receive object with the following structure:\n     * {{\n     * roomName: room //the room name of the conference\n     * }}\n     * {@code videoConferenceLeft} - receives event notifications about the\n     * local user has left the video conference.\n     * The listener will receive object with the following structure:\n     * {{\n     * roomName: room //the room name of the conference\n     * }}\n     * {@code screenSharingStatusChanged} - receives event notifications about\n     * turning on/off the local user screen sharing.\n     * The listener will receive object with the following structure:\n     * {{\n     * on: on //whether screen sharing is on\n     * }}\n     * {@code dominantSpeakerChanged} - receives event notifications about\n     * change in the dominant speaker.\n     * The listener will receive object with the following structure:\n     * {{\n     * id: participantId //participantId of the new dominant speaker\n     * }}\n     * {@code suspendDetected} - receives event notifications about detecting suspend event in host computer.\n     * {@code readyToClose} - all hangup operations are completed and Jitsi Meet\n     * is ready to be disposed.\n     * @returns {void}\n     *\n     * @deprecated\n     * NOTE: This method is not removed for backward comatability purposes.\n     */\n    addEventListeners(listeners) {\n        for (const event in listeners) { // eslint-disable-line guard-for-in\n            this.addEventListener(event, listeners[event]);\n        }\n    }\n\n    /**\n     * Captures the screenshot of the large video.\n     *\n     * @returns {Promise<string>} - Resolves with a base64 encoded image data of the screenshot\n     * if large video is detected, an error otherwise.\n     */\n    captureLargeVideoScreenshot() {\n        return this._transport.sendRequest({\n            name: 'capture-largevideo-screenshot'\n        });\n    }\n\n    /**\n     * Removes the listeners and removes the Jitsi Meet frame.\n     *\n     * @returns {void}\n     */\n    dispose() {\n        this.emit('_willDispose');\n        this._transport.dispose();\n        this.removeAllListeners();\n        if (this._frame && this._frame.parentNode) {\n            this._frame.parentNode.removeChild(this._frame);\n        }\n    }\n\n    /**\n     * Executes command. The available commands are:\n     * {@code displayName} - Sets the display name of the local participant to\n     * the value passed in the arguments array.\n     * {@code subject} - Sets the subject of the conference, the value passed\n     * in the arguments array. Note: Available only for moderator.\n     *\n     * {@code toggleAudio} - Mutes / unmutes audio with no arguments.\n     * {@code toggleVideo} - Mutes / unmutes video with no arguments.\n     * {@code toggleFilmStrip} - Hides / shows the filmstrip with no arguments.\n     *\n     * If the command doesn't require any arguments the parameter should be set\n     * to empty array or it may be omitted.\n     *\n     * @param {string} name - The name of the command.\n     * @returns {void}\n     */\n    executeCommand(name, ...args) {\n        if (!(name in commands)) {\n            console.error('Not supported command name.');\n\n            return;\n        }\n        this._transport.sendEvent({\n            data: args,\n            name: commands[name]\n        });\n    }\n\n    /**\n     * Executes commands. The available commands are:\n     * {@code displayName} - Sets the display name of the local participant to\n     * the value passed in the arguments array.\n     * {@code toggleAudio} - Mutes / unmutes audio. No arguments.\n     * {@code toggleVideo} - Mutes / unmutes video. No arguments.\n     * {@code toggleFilmStrip} - Hides / shows the filmstrip. No arguments.\n     * {@code toggleChat} - Hides / shows chat. No arguments.\n     * {@code toggleShareScreen} - Starts / stops screen sharing. No arguments.\n     *\n     * @param {Object} commandList - The object with commands to be executed.\n     * The keys of the object are the commands that will be executed and the\n     * values are the arguments for the command.\n     * @returns {void}\n     */\n    executeCommands(commandList) {\n        for (const key in commandList) { // eslint-disable-line guard-for-in\n            this.executeCommand(key, commandList[key]);\n        }\n    }\n\n    /**\n     * Returns Promise that resolves with a list of available devices.\n     *\n     * @returns {Promise}\n     */\n    getAvailableDevices() {\n        return getAvailableDevices(this._transport);\n    }\n\n    /**\n     * Gets a list of the currently sharing participant id's.\n     *\n     * @returns {Promise} - Resolves with the list of participant id's currently sharing.\n     */\n    getContentSharingParticipants() {\n        return this._transport.sendRequest({\n            name: 'get-content-sharing-participants'\n        });\n    }\n\n    /**\n     * Returns Promise that resolves with current selected devices.\n     *\n     * @returns {Promise}\n     */\n    getCurrentDevices() {\n        return getCurrentDevices(this._transport);\n    }\n\n    /**\n     * Returns any custom avatars backgrounds.\n     *\n     * @returns {Promise} - Resolves with the list of custom avatar backgrounds.\n     */\n    getCustomAvatarBackgrounds() {\n        return this._transport.sendRequest({\n            name: 'get-custom-avatar-backgrounds'\n        });\n    }\n\n    /**\n     * Returns the current livestream url.\n     *\n     * @returns {Promise} - Resolves with the current livestream URL if exists, with\n     * undefined if not and rejects on failure.\n     */\n    getLivestreamUrl() {\n        return this._transport.sendRequest({\n            name: 'get-livestream-url'\n        });\n    }\n\n    /**\n     * Returns the conference participants information.\n     *\n     * @returns {Array<Object>} - Returns an array containing participants\n     * information like participant id, display name, avatar URL and email.\n     */\n    getParticipantsInfo() {\n        const participantIds = Object.keys(this._participants);\n        const participantsInfo = Object.values(this._participants);\n\n        participantsInfo.forEach((participant, idx) => {\n            participant.participantId = participantIds[idx];\n        });\n\n        return participantsInfo;\n    }\n\n    /**\n     * Returns the current video quality setting.\n     *\n     * @returns {number}\n     */\n    getVideoQuality() {\n        return this._videoQuality;\n    }\n\n    /**\n     * Check if the audio is available.\n     *\n     * @returns {Promise} - Resolves with true if the audio available, with\n     * false if not and rejects on failure.\n     */\n    isAudioAvailable() {\n        return this._transport.sendRequest({\n            name: 'is-audio-available'\n        });\n    }\n\n    /**\n     * Returns Promise that resolves with true if the device change is available\n     * and with false if not.\n     *\n     * @param {string} [deviceType] - Values - 'output', 'input' or undefined.\n     * Default - 'input'.\n     * @returns {Promise}\n     */\n    isDeviceChangeAvailable(deviceType) {\n        return isDeviceChangeAvailable(this._transport, deviceType);\n    }\n\n    /**\n     * Returns Promise that resolves with true if the device list is available\n     * and with false if not.\n     *\n     * @returns {Promise}\n     */\n    isDeviceListAvailable() {\n        return isDeviceListAvailable(this._transport);\n    }\n\n    /**\n     * Returns Promise that resolves with true if multiple audio input is supported\n     * and with false if not.\n     *\n     * @returns {Promise}\n     */\n    isMultipleAudioInputSupported() {\n        return isMultipleAudioInputSupported(this._transport);\n    }\n\n    /**\n     * Invite people to the call.\n     *\n     * @param {Array<Object>} invitees - The invitees.\n     * @returns {Promise} - Resolves on success and rejects on failure.\n     */\n    invite(invitees) {\n        if (!Array.isArray(invitees) || invitees.length === 0) {\n            return Promise.reject(new TypeError('Invalid Argument'));\n        }\n\n        return this._transport.sendRequest({\n            name: 'invite',\n            invitees\n        });\n    }\n\n    /**\n     * Returns the audio mute status.\n     *\n     * @returns {Promise} - Resolves with the audio mute status and rejects on\n     * failure.\n     */\n    isAudioMuted() {\n        return this._transport.sendRequest({\n            name: 'is-audio-muted'\n        });\n    }\n\n    /**\n     * Returns the audio disabled status.\n     *\n     * @returns {Promise} - Resolves with the audio disabled status and rejects on\n     * failure.\n     */\n    isAudioDisabled() {\n        return this._transport.sendRequest({\n            name: 'is-audio-disabled'\n        });\n    }\n\n    /**\n     * Returns the moderation on status on the given mediaType.\n     *\n     * @param {string} mediaType - The media type for which to check moderation.\n     * @returns {Promise} - Resolves with the moderation on status and rejects on\n     * failure.\n     */\n    isModerationOn(mediaType) {\n        return this._transport.sendRequest({\n            name: 'is-moderation-on',\n            mediaType\n        });\n    }\n\n    /**\n     * Returns force muted status of the given participant id for the given media type.\n     *\n     * @param {string} participantId - The id of the participant to check.\n     * @param {string} mediaType - The media type for which to check.\n     * @returns {Promise} - Resolves with the force muted status and rejects on\n     * failure.\n     */\n    isParticipantForceMuted(participantId, mediaType) {\n        return this._transport.sendRequest({\n            name: 'is-participant-force-muted',\n            participantId,\n            mediaType\n        });\n    }\n\n    /**\n     * Returns whether the participants pane is open.\n     *\n     * @returns {Promise} - Resolves with true if the participants pane is open\n     * and with false if not.\n     */\n    isParticipantsPaneOpen() {\n        return this._transport.sendRequest({\n            name: 'is-participants-pane-open'\n        });\n    }\n\n    /**\n     * Returns screen sharing status.\n     *\n     * @returns {Promise} - Resolves with screensharing status and rejects on failure.\n     */\n    isSharingScreen() {\n        return this._transport.sendRequest({\n            name: 'is-sharing-screen'\n        });\n    }\n\n    /**\n     * Returns whether meeting is started silent.\n     *\n     * @returns {Promise} - Resolves with start silent status.\n     */\n    isStartSilent() {\n        return this._transport.sendRequest({\n            name: 'is-start-silent'\n        });\n    }\n\n    /**\n     * Returns the avatar URL of a participant.\n     *\n     * @param {string} participantId - The id of the participant.\n     * @returns {string} The avatar URL.\n     */\n    getAvatarURL(participantId) {\n        const { avatarURL } = this._participants[participantId] || {};\n\n        return avatarURL;\n    }\n\n    /**\n     * Gets the deployment info.\n     *\n     * @returns {Promise} - Resolves with the deployment info object.\n     */\n    getDeploymentInfo() {\n        return this._transport.sendRequest({\n            name: 'deployment-info'\n        });\n    }\n\n    /**\n     * Returns the display name of a participant.\n     *\n     * @param {string} participantId - The id of the participant.\n     * @returns {string} The display name.\n     */\n    getDisplayName(participantId) {\n        const { displayName } = this._participants[participantId] || {};\n\n        return displayName;\n    }\n\n    /**\n     * Returns the email of a participant.\n     *\n     * @param {string} participantId - The id of the participant.\n     * @returns {string} The email.\n     */\n    getEmail(participantId) {\n        const { email } = this._participants[participantId] || {};\n\n        return email;\n    }\n\n    /**\n     * Returns the iframe that loads Jitsi Meet.\n     *\n     * @returns {HTMLElement} The iframe.\n     */\n    getIFrame() {\n        return this._frame;\n    }\n\n    /**\n     * Returns the number of participants in the conference from all rooms. The local\n     * participant is included.\n     *\n     * @returns {int} The number of participants in the conference.\n     */\n    getNumberOfParticipants() {\n        return this._numberOfParticipants;\n    }\n\n    /**\n     * Check if the video is available.\n     *\n     * @returns {Promise} - Resolves with true if the video available, with\n     * false if not and rejects on failure.\n     */\n    isVideoAvailable() {\n        return this._transport.sendRequest({\n            name: 'is-video-available'\n        });\n    }\n\n    /**\n     * Returns the audio mute status.\n     *\n     * @returns {Promise} - Resolves with the audio mute status and rejects on\n     * failure.\n     */\n    isVideoMuted() {\n        return this._transport.sendRequest({\n            name: 'is-video-muted'\n        });\n    }\n\n    /**\n     * Returns the list of breakout rooms.\n     *\n     * @returns {Promise} Resolves with the list of breakout rooms.\n     */\n    listBreakoutRooms() {\n        return this._transport.sendRequest({\n            name: 'list-breakout-rooms'\n        });\n    }\n\n    /**\n     * Pins a participant's video on to the stage view.\n     *\n     * @param {string} participantId - Participant id (JID) of the participant\n     * that needs to be pinned on the stage view.\n     * @param {string} [videoType] - Indicates the type of thumbnail to be pinned when multistream support is enabled.\n     * Accepts \"camera\" or \"desktop\" values. Default is \"camera\". Any invalid values will be ignored and default will\n     * be used.\n     * @returns {void}\n     */\n    pinParticipant(participantId, videoType) {\n        this.executeCommand('pinParticipant', participantId, videoType);\n    }\n\n    /**\n     * Removes event listener.\n     *\n     * @param {string} event - The name of the event.\n     * @returns {void}\n     *\n     * @deprecated\n     * NOTE: This method is not removed for backward comatability purposes.\n     */\n    removeEventListener(event) {\n        this.removeAllListeners(event);\n    }\n\n    /**\n     * Removes event listeners.\n     *\n     * @param {Array<string>} eventList - Array with the names of the events.\n     * @returns {void}\n     *\n     * @deprecated\n     * NOTE: This method is not removed for backward comatability purposes.\n     */\n    removeEventListeners(eventList) {\n        eventList.forEach(event => this.removeEventListener(event));\n    }\n\n    /**\n     * Resizes the large video container as per the dimensions provided.\n     *\n     * @param {number} width - Width that needs to be applied on the large video container.\n     * @param {number} height - Height that needs to be applied on the large video container.\n     * @returns {void}\n     */\n    resizeLargeVideo(width, height) {\n        if (width <= this._width && height <= this._height) {\n            this.executeCommand('resizeLargeVideo', width, height);\n        }\n    }\n\n    /**\n     * Passes an event along to the local conference participant to establish\n     * or update a direct peer connection. This is currently used for developing\n     * wireless screensharing with room integration and it is advised against to\n     * use as its api may change.\n     *\n     * @param {Object} event - An object with information to pass along.\n     * @param {Object} event.data - The payload of the event.\n     * @param {string} event.from - The jid of the sender of the event. Needed\n     * when a reply is to be sent regarding the event.\n     * @returns {void}\n     */\n    sendProxyConnectionEvent(event) {\n        this._transport.sendEvent({\n            data: [ event ],\n            name: 'proxy-connection-event'\n        });\n    }\n\n    /**\n     * Sets the audio input device to the one with the label or id that is\n     * passed.\n     *\n     * @param {string} label - The label of the new device.\n     * @param {string} deviceId - The id of the new device.\n     * @returns {Promise}\n     */\n    setAudioInputDevice(label, deviceId) {\n        return setAudioInputDevice(this._transport, label, deviceId);\n    }\n\n    /**\n     * Sets the audio output device to the one with the label or id that is\n     * passed.\n     *\n     * @param {string} label - The label of the new device.\n     * @param {string} deviceId - The id of the new device.\n     * @returns {Promise}\n     */\n    setAudioOutputDevice(label, deviceId) {\n        return setAudioOutputDevice(this._transport, label, deviceId);\n    }\n\n    /**\n     * Displays the given participant on the large video. If no participant id is specified,\n     * dominant and pinned speakers will be taken into consideration while selecting the\n     * the large video participant.\n     *\n     * @param {string} participantId - Jid of the participant to be displayed on the large video.\n     * @param {string} [videoType] - Indicates the type of video to be set when multistream support is enabled.\n     * Accepts \"camera\" or \"desktop\" values. Default is \"camera\". Any invalid values will be ignored and default will\n     * be used.\n     * @returns {void}\n     */\n    setLargeVideoParticipant(participantId, videoType) {\n        this.executeCommand('setLargeVideoParticipant', participantId, videoType);\n    }\n\n    /**\n     * Sets the video input device to the one with the label or id that is\n     * passed.\n     *\n     * @param {string} label - The label of the new device.\n     * @param {string} deviceId - The id of the new device.\n     * @returns {Promise}\n     */\n    setVideoInputDevice(label, deviceId) {\n        return setVideoInputDevice(this._transport, label, deviceId);\n    }\n\n    /**\n     * Starts a file recording or streaming session depending on the passed on params.\n     * For RTMP streams, `rtmpStreamKey` must be passed on. `rtmpBroadcastID` is optional.\n     * For youtube streams, `youtubeStreamKey` must be passed on. `youtubeBroadcastID` is optional.\n     * For dropbox recording, recording `mode` should be `file` and a dropbox oauth2 token must be provided.\n     * For file recording, recording `mode` should be `file` and optionally `shouldShare` could be passed on.\n     * No other params should be passed.\n     *\n     * @param {Object} options - An object with config options to pass along.\n     * @param { string } options.mode - Recording mode, either `file` or `stream`.\n     * @param { string } options.dropboxToken - Dropbox oauth2 token.\n     * @param { boolean } options.shouldShare - Whether the recording should be shared with the participants or not.\n     * Only applies to certain jitsi meet deploys.\n     * @param { string } options.rtmpStreamKey - The RTMP stream key.\n     * @param { string } options.rtmpBroadcastID - The RTMP broadcast ID.\n     * @param { string } options.youtubeStreamKey - The youtube stream key.\n     * @param { string } options.youtubeBroadcastID - The youtube broadcast ID.\n     * @returns {void}\n     */\n    startRecording(options) {\n        this.executeCommand('startRecording', options);\n    }\n\n    /**\n     * Stops a recording or streaming session that is in progress.\n     *\n     * @param {string} mode - `file` or `stream`.\n     * @returns {void}\n     */\n    stopRecording(mode) {\n        this.executeCommand('stopRecording', mode);\n    }\n\n    /**\n     * Sets e2ee enabled/disabled.\n     *\n     * @param {boolean} enabled - The new value for e2ee enabled.\n     * @returns {void}\n     */\n    toggleE2EE(enabled) {\n        this.executeCommand('toggleE2EE', enabled);\n    }\n\n    /**\n     * Sets the key and keyIndex for e2ee.\n     *\n     * @param {Object} keyInfo - Json containing key information.\n     * @param {CryptoKey} [keyInfo.encryptionKey] - The encryption key.\n     * @param {number} [keyInfo.index] - The index of the encryption key.\n     * @returns {void}\n     */\n    async setMediaEncryptionKey(keyInfo) {\n        const { key, index } = keyInfo;\n\n        if (key) {\n            const exportedKey = await crypto.subtle.exportKey('raw', key);\n\n            this.executeCommand('setMediaEncryptionKey', JSON.stringify({\n                exportedKey: Array.from(new Uint8Array(exportedKey)),\n                index }));\n        } else {\n            this.executeCommand('setMediaEncryptionKey', JSON.stringify({\n                exportedKey: false,\n                index }));\n        }\n    }\n}\n", "// For legacy purposes, preserve the UMD of the public API of Jitsi Meet\n// external API (a.k.a. JitsiMeetExternalAPI).\nmodule.exports = require('./external_api').default;\n", "'use strict';\n\n\nconst internals = {\n    suspectRx: /\"(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])\"\\s*\\:/\n};\n\n\nexports.parse = function (text, ...args) {\n\n    // Normalize arguments\n\n    const firstOptions = typeof args[0] === 'object' && args[0];\n    const reviver = args.length > 1 || !firstOptions ? args[0] : undefined;\n    const options = (args.length > 1 && args[1]) || firstOptions || {};\n\n    // Parse normally, allowing exceptions\n\n    const obj = JSON.parse(text, reviver);\n\n    // options.protoAction: 'error' (default) / 'remove' / 'ignore'\n\n    if (options.protoAction === 'ignore') {\n        return obj;\n    }\n\n    // Ignore null and non-objects\n\n    if (!obj ||\n        typeof obj !== 'object') {\n\n        return obj;\n    }\n\n    // Check original string for potential exploit\n\n    if (!text.match(internals.suspectRx)) {\n        return obj;\n    }\n\n    // Scan result for proto keys\n\n    exports.scan(obj, options);\n\n    return obj;\n};\n\n\nexports.scan = function (obj, options = {}) {\n\n    let next = [obj];\n\n    while (next.length) {\n        const nodes = next;\n        next = [];\n\n        for (const node of nodes) {\n            if (Object.prototype.hasOwnProperty.call(node, '__proto__')) {      // Avoid calling node.hasOwnProperty directly\n                if (options.protoAction !== 'remove') {\n                    throw new SyntaxError('Object contains forbidden prototype property');\n                }\n\n                delete node.__proto__;\n            }\n\n            for (const key in node) {\n                const value = node[key];\n                if (value &&\n                    typeof value === 'object') {\n\n                    next.push(node[key]);\n                }\n            }\n        }\n    }\n};\n\n\nexports.safeParse = function (text, reviver) {\n\n    try {\n        return exports.parse(text, reviver);\n    }\n    catch (ignoreError) {\n        return null;\n    }\n};\n", "/* Copyright @ 2016-present 8x8, Inc.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar Logger = require('./Logger.js');\n\n/**\n * Creates new <tt>LogCollector</tt>. Class implements <tt>LoggerTransport</tt>\n * and thus can be added as global transport in order to capture all the logs.\n *\n * It captures subsequent log lines created whenever <tt>Logger</tt> logs\n * a message and stores them in a queue in order to batch log entries. There are\n * time and size limit constraints which determine how often batch entries are\n * stored. Whenever one of these limits is exceeded the <tt>LogCollector</tt>\n * will use the <tt>logStorage</tt> object given as an argument to save\n * the batch log entry.\n *\n * @param {Object} logStorage an object which allows to store the logs collected\n * @param {function(string|object[])} logStorage.storeLogs a method called when\n * this <tt>LogCollector</tt> requests log entry storage. The method's argument\n * is an array which can contain <tt>string</tt>s and <tt>object</tt>s. If given\n * item is an object it means that it's an aggregated message. That is a message\n * which is the same as the previous one and it's representation has\n * the following format:\n * {\n *   {string} text: 'the text of some duplicated message'\n *   {number} count: 3 // how many times the message appeared in a row\n * }\n * If a message \"B\" after an aggregated message \"A\" is different, then it breaks\n * the sequence of \"A\". Which means that even if the next message \"C\" is\n * the same as \"A\" it will start a new aggregated message \"C\".\n * @param {function()} logStorage.isReady a method which should return\n * a <tt>boolean</tt> to tell the collector that it's ready to store. During the\n * time storage is not ready log batches will be cached and stored on the next\n * occasion (flush or interval timeout).\n *\n * @param {Object} options the <tt>LogCollector</tt> configuration options.\n * @param {number} options.maxEntryLength the size limit for a single log entry\n * to be stored. The <tt>LogCollector</tt> will push the entry as soon as it\n * reaches or exceeds this limit given that <tt>logStorage.isReady</tt>\n * returns <tt>true</tt>. Otherwise the log entry will be cached until the log\n * storage becomes ready. Note that the \"is ready\" condition is checked every\n * <tt>options.storeInterval</tt> milliseconds.\n * @param {number} options.storeInterval how often the logs should be stored in\n * case <tt>maxEntryLength</tt> was not exceeded.\n * @param {boolean} options.stringifyObjects indicates whether or not object\n * arguments should be \"stringified\" with <tt>JSON.stringify</tt> when a log\n * message is composed. Note that objects logged on the error log level are\n * always stringified.\n *\n * @constructor\n */\nfunction LogCollector(logStorage, options) {\n    this.logStorage = logStorage;\n    this.stringifyObjects = options && options.stringifyObjects ? options.stringifyObjects : false;\n    this.storeInterval = options && options.storeInterval ? options.storeInterval: 30000;\n    this.maxEntryLength = options && options.maxEntryLength ? options.maxEntryLength : 10000;\n    // Bind the log method for each level to the corresponding method name\n    // in order to implement \"global log transport\" object.\n    Object.keys(Logger.levels).forEach(\n    function (logLevel) {\n        var methodName = Logger.levels[logLevel];\n        this[methodName] = function () {\n            this._log.apply(this, arguments);\n        }.bind(this, logLevel);\n    }.bind(this));\n    /**\n     * The ID of store logs interval if one is currently scheduled or\n     * <tt>null</tt> otherwise.\n     * @type {number|null}\n     */\n    this.storeLogsIntervalID = null;\n    /**\n     * The log messages that are to be batched into log entry when\n     * {@link LogCollector._flush} method is called.\n     * @type {string[]}\n     */\n    this.queue = [];\n    /**\n     * The total length of all messages currently stored in the {@link queue}.\n     * @type {number}\n     */\n    this.totalLen = 0;\n    /**\n     * An array used to temporarily store log batches, before the storage gets\n     * ready.\n     * @type {string[]}\n     */\n    this.outputCache = [];\n}\n\n/**\n * Method called inside of {@link formatLogMessage} in order to covert an\n * <tt>Object</tt> argument to string. The conversion will happen when either\n * 'stringifyObjects' option is enabled or on the {@link Logger.levels.ERROR}\n * log level. The default implementation uses <tt>JSON.stringify</tt> and\n * returns \"[object with circular refs?]\" instead of an object if it fails.\n *\n * @param {object} someObject the <tt>object</tt> to be stringified.\n *\n * @return {string} the result of <tt>JSON.stringify</tt> or\n * \"[object with circular refs?]\" if any error occurs during \"stringification\".\n *\n * @protected\n */\nLogCollector.prototype.stringify = function (someObject) {\n    try {\n        return JSON.stringify(someObject);\n    } catch (error) {\n        return '[object with circular refs?]';\n    }\n};\n\n/**\n * Formats log entry for the given logging level and arguments passed to the\n * <tt>Logger</tt>'s log method. The first argument is log level and the next\n * arguments have to be captured using JS built-in 'arguments' variable.\n *\n * @param {Logger.levels} logLevel provides the logging level of the message to\n * be logged.\n * @param {Date} timestamp - The {@code Date} when a message has been logged.\n *\n * @return {string|null} a non-empty string representation of the log entry\n * crafted from the log arguments. If the return value is <tt>null</tt> then\n * the message wil be discarded by this <tt>LogCollector</tt>.\n *\n * @protected\n */\nLogCollector.prototype.formatLogMessage = function (\nlogLevel /* timestamp, arg2, arg3, arg4... */) {\n    var msg = '';\n    for (var i = 1, len = arguments.length; i < len; i++) {\n        var arg = arguments[i];\n        // objects logged on error level are always converted to JSON\n        if ((this.stringifyObjects || logLevel === Logger.levels.ERROR) &&\n            typeof arg === 'object') {\n            arg = this.stringify(arg);\n        }\n        msg += arg;\n        if (i !== len - 1) {\n            msg += ' ';\n        }\n    }\n    return msg.length ? msg : null;\n};\n\n/**\n * The log method bound to each of the logging levels in order to implement\n * \"global log transport\" object.\n *\n * @private\n */\nLogCollector.prototype._log = function() {\n\n    // var logLevel = arguments[0]; first argument is the log level\n    var timestamp = arguments[1];\n    var msg = this.formatLogMessage.apply(this, arguments);\n    if (msg) {\n        // The same as the previous message aggregation logic\n        var prevMessage = this.queue[this.queue.length - 1];\n        var prevMessageText = prevMessage && prevMessage.text;\n        if (prevMessageText === msg) {\n            prevMessage.count += 1;\n        } else {\n            this.queue.push({\n                text: msg,\n                timestamp: timestamp,\n                count: 1\n            });\n            this.totalLen += msg.length;\n        }\n    }\n\n    if (this.totalLen >= this.maxEntryLength) {\n        this._flush(true /* force */, true /* reschedule */);\n    }\n};\n\n/**\n * Starts periodical \"store logs\" task which will be triggered at the interval\n * specified in the constructor options.\n */\nLogCollector.prototype.start = function () {\n    this._reschedulePublishInterval();\n};\n\n/**\n * Reschedules the periodical \"store logs\" task which will store the next batch\n * log entry in the storage.\n * @private\n */\nLogCollector.prototype._reschedulePublishInterval = function () {\n    if (this.storeLogsIntervalID) {\n        window.clearTimeout(this.storeLogsIntervalID);\n        this.storeLogsIntervalID = null;\n    }\n    // It's actually a timeout, because it is rescheduled on every flush\n    this.storeLogsIntervalID = window.setTimeout(\n        this._flush.bind(\n            this, false /* do not force */, true /* reschedule */),\n        this.storeInterval);\n};\n\n/**\n * Call this method to flush the log entry buffer and store it in the log\n * storage immediately (given that the storage is ready).\n */\nLogCollector.prototype.flush = function() {\n    this._flush(\n        false /* do not force, as it will not be stored anyway */,\n        true /* reschedule next update */ );\n};\n\n/**\n * Stores the next batch log entry in the log storage.\n * @param {boolean} force enforce current logs batch to be stored or cached if\n * there is anything to be logged, but the storage is not ready yet. One of\n * legitimate reasons to force is when the logs length exceeds size limit which\n * could result in truncation.\n * @param {boolean} reschedule <tt>true</tt> if the next periodic task should be\n * scheduled after the log entry is stored. <tt>false</tt> will end the periodic\n * task cycle.\n * @private\n */\nLogCollector.prototype._flush = function(force, reschedule) {\n    // Publish only if there's anything to be logged\n    if (this.totalLen > 0 && (this.logStorage.isReady() || force)) {\n        //FIXME avoid truncating\n        // right now we don't care if the message size is \"slightly\" exceeded\n        if (this.logStorage.isReady()) {\n            // Sends all cached logs\n            if (this.outputCache.length) {\n                this.outputCache.forEach(\n                    function (cachedQueue) {\n                        this.logStorage.storeLogs(cachedQueue);\n                    }.bind(this)\n                );\n                // Clear the cache\n                this.outputCache = [];\n            }\n            // Send current batch\n            this.logStorage.storeLogs(this.queue);\n        } else {\n            this.outputCache.push(this.queue);\n        }\n\n        this.queue = [];\n        this.totalLen = 0;\n    }\n\n    if (reschedule) {\n        this._reschedulePublishInterval();\n    }\n};\n\n/**\n * Stops the periodical \"store logs\" task and immediately stores any pending\n * log entries as a batch.\n */\nLogCollector.prototype.stop = function() {\n    // Flush and stop publishing logs\n    this._flush(false /* do not force */, false /* do not reschedule */);\n};\n\nmodule.exports = LogCollector;\n", "/* Copyright @ 2015-present 8x8, Inc.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*jslint latedef:false*/\n\n/**\n * Ordered log levels.\n */\nvar levels = {\n    \"trace\": 0,\n    \"debug\": 1,\n    \"info\": 2,\n    \"log\": 3,\n    \"warn\": 4,\n    \"error\": 5\n};\n\n/**\n * The default transport - console\n * @type LoggerTransport\n */\nLogger.consoleTransport = console;\n\n/**\n * The array which stores currently registered global transports.\n * @type {[LoggerTransport]}\n */\nvar globalTransports = [ Logger.consoleTransport ];\n\n/**\n * Adds given {@link LoggerTransport} instance to the list of global\n * transports which means that it'll be used by all {@link Logger}s\n * @param {LoggerTransport} transport\n */\nLogger.addGlobalTransport = function(transport) {\n    if (globalTransports.indexOf(transport) === -1) {\n        globalTransports.push(transport);\n    }\n};\n\n/**\n * Removes given {@link LoggerTransport} instance from the list of global\n * transports\n * @param {LoggerTransport} transport\n */\nLogger.removeGlobalTransport = function(transport) {\n    var transportIdx = globalTransports.indexOf(transport);\n    if (transportIdx !== -1) {\n        globalTransports.splice(transportIdx, 1);\n    }\n};\n\n/**\n * The global configuration options.\n */\nvar globalOptions = {};\n\n/**\n * Sets global options which will be used by all loggers. Changing these works\n * even after other loggers are created.\n */\nLogger.setGlobalOptions = function(options) {\n    globalOptions = options || {};\n};\n\n/**\n * Parses Error's object stack trace and extracts information about the last\n * caller before the log method was called.\n * @returns JS object with info about the caller - method name, file location,\n * line and column.\n */\nfunction getCallerInfo() {\n    var callerInfo = {\n        methodName: \"\",\n        fileLocation: \"\",\n        line: null,\n        column: null\n    };\n    //gets the part of the stack without the logger wrappers\n    var error = new Error();\n    var stack = error.stack? error.stack.split(\"\\n\") : [];\n    if(!stack || stack.length < 3) {\n        return callerInfo;\n    }\n    var m = null;\n    if(stack[3]) {\n        m = stack[3].match(/\\s*at\\s*(.+?)\\s*\\((\\S*)\\s*:(\\d*)\\s*:(\\d*)\\)/);\n    }\n    if(!m || m.length <= 4) {\n        //Firefox && Safari\n        if(stack[2].indexOf(\"log@\") === 0){\n            //Safari\n            callerInfo.methodName = stack[3].substr(0, stack[3].indexOf(\"@\"));\n        } else {\n            //Firefox\n            callerInfo.methodName = stack[2].substr(0, stack[2].indexOf(\"@\"));\n        }\n        return callerInfo;\n    }\n\n    callerInfo.methodName = m[1];\n    callerInfo.fileLocation = m[2];\n    callerInfo.line = m[3];\n    callerInfo.column = m[4];\n    return callerInfo;\n}\n\n/**\n * Logs messages using the transports and level from the logger.\n * @param logger a logger instance.\n * @param level the log level of the message. See the levels variable.\n * @param arguments array with arguments that will be logged.\n */\nfunction log() {\n    var logger = arguments[0], level = arguments[1],\n        args = Array.prototype.slice.call(arguments, 2);\n    if(levels[level] < logger.level) {\n        return;\n    }\n\n    var callerInfo\n        = !(logger.options.disableCallerInfo || globalOptions.disableCallerInfo) &&\n            getCallerInfo();\n    var transports = globalTransports.concat(logger.transports);\n    for(var i = 0; i < transports.length; i++) {\n        var t = transports[i];\n        var l = t[level];\n        if(l && typeof(l) === \"function\") {\n            var logPrefixes = [];\n\n            logPrefixes.push(new Date().toISOString());\n\n            if (logger.id) {\n                logPrefixes.push(\"[\" + logger.id + \"]\");\n            }\n\n            if (callerInfo && callerInfo.methodName.length > 1) {\n                logPrefixes.push(\"<\" + callerInfo.methodName + \">: \");\n            }\n\n            var fullLogParts = logPrefixes.concat(args);\n\n            l.bind(t).apply(t, fullLogParts);\n        }\n    }\n}\n\n/**\n *\n * Constructs new logger object.\n * @param level the logging level for the new logger\n * @param id optional identifier for the logger instance.\n * @param {LoggerTransport} transports optional list of handlers(objects) for\n * the logs. The handlers must support - log, warn, error, debug, info, trace.\n * @param options optional configuration file for how the logger should behave.\n * @param {boolean} options.disableCallerInfo Whether the call site of a logger\n * method invocation should be included in the log. Defaults to false, so the\n * call site will be included.\n */\nfunction Logger(level, id, transports, options) {\n    this.id = id;\n    this.options = options || {};\n    this.transports = transports;\n    if(!this.transports) {\n        this.transports = [];\n    }\n    this.level = levels[level];\n    var methods = Object.keys(levels);\n    for(var i = 0; i < methods.length; i++){\n        this[methods[i]] =\n            log.bind(null, this, methods[i]);\n    }\n}\n\n/**\n * Sets the log level for the logger.\n * @param level the new log level.\n */\nLogger.prototype.setLevel = function (level) {\n    this.level = levels[level];\n};\nmodule.exports = Logger;\n\n/**\n * Enum for the supported log levels.\n */\nLogger.levels = {\n    TRACE: \"trace\",\n    DEBUG: \"debug\",\n    INFO: \"info\",\n    LOG: \"log\",\n    WARN: \"warn\",\n    ERROR: \"error\"\n};\n", "/* Copyright @ 2015-present 8x8, Inc.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar Logger = require(\"./Logger\");\nvar LogCollector = require(\"./LogCollector\");\n\n/**\n * Definition of the log method\n * @name log_method\n * @function\n * @param {...*} log_args the arguments to be logged\n */\n/**\n * The logger's transport type definition.\n *\n * @typedef {object} LoggerTransport\n *\n * @property {log_method} trace method called to log on {@link Logger.levels.TRACE} logging level\n * @property {log_method} debug method called to log on {@link Logger.levels.DEBUG} logging level\n * @property {log_method} info method called to log on {@link Logger.levels.INFO} logging level\n * @property {log_method} log method called to log on {@link Logger.levels.LOG} logging level\n * @property {log_method} warn method called to log on {@link Logger.levels.WARN} logging level\n * @property {log_method} error method called to log on {@link Logger.levels.ERROR} logging level\n */\n\n/**\n * Map with the created loggers with ID.\n */\nvar idLoggers = {};\n\n/**\n * Array with the loggers without id.\n */\nvar loggers = [];\n\n/**\n * Log level for the lbrary.\n */\nvar curLevel = Logger.levels.TRACE;\n\n\nmodule.exports = {\n    /**\n     * Adds given {@link LoggerTransport} instance to the list of global\n     * transports which means that it'll be used by all {@link Logger}s\n     * @param {LoggerTransport} transport\n     */\n    addGlobalTransport: function(transport) {\n        Logger.addGlobalTransport(transport);\n    },\n    /**\n     * Removes given {@link LoggerTransport} instance from the list of global\n     * transports\n     * @param {LoggerTransport} transport\n     */\n    removeGlobalTransport: function(transport) {\n        Logger.removeGlobalTransport(transport);\n    },\n    /**\n    * Sets global options which will be used by all loggers. Changing these\n    * works even after other loggers are created.\n    */\n    setGlobalOptions: function(options) {\n        Logger.setGlobalOptions(options);\n    },\n    /**\n     * Creates new logger.\n     * @arguments the same as Logger constructor\n     */\n    getLogger: function(id, transports, options) {\n        var logger = new Logger(curLevel, id, transports, options);\n        if(id) {\n            idLoggers[id] = idLoggers[id] || [];\n            idLoggers[id].push(logger);\n        } else {\n            loggers.push(logger);\n        }\n        return logger;\n    },\n    /**\n     * Changes the log level for the existing loggers by id.\n     * @param level the new log level.\n     * @param id if specified the level will be changed only for loggers with the\n     * same id. Otherwise the operation will affect all loggers that don't\n     * have id.\n     */\n    setLogLevelById: function(level, id) {\n        var l = id? (idLoggers[id] || []) : loggers;\n        for(var i = 0; i < l.length; i++) {\n            l[i].setLevel(level);\n        }\n    },\n    /**\n     * Changes the log level for all existing loggers.\n     * @param level the new log level.\n     */\n    setLogLevel: function (level) {\n        curLevel = level;\n        var i = 0;\n        for(; i < loggers.length; i++) {\n            loggers[i].setLevel(level);\n        }\n\n        for(var id in idLoggers) {\n            var l = idLoggers[id] || [];\n            for(i = 0; i < l.length; i++) {\n                l[i].setLevel(level);\n            }\n        }\n    },\n    /**\n     * The supported log levels.\n     */\n    levels: Logger.levels,\n    /**\n     * Exports the <tt>LogCollector</tt>.\n     */\n    LogCollector: LogCollector\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// startup\n// Load entry module and return exports\n// This entry module used 'module' so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(872);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "DummyLocalStorage", "EventEmitter", "clear", "this", "_storage", "length", "Object", "keys", "getItem", "keyName", "setItem", "keyValue", "removeItem", "key", "n", "serialize", "ignore", "JSON", "stringify", "storageCopy", "for<PERSON>ach", "jitsiLocalStorage", "constructor", "super", "window", "localStorage", "_localStorageDisabled", "console", "warn", "isLocalStorageDisabled", "emit", "dontEmitChangedEvent", "i", "localStorageContent", "includes", "blacklist", "StatusCode", "_fixURIStringScheme", "uri", "regex", "RegExp", "match", "exec", "protocol", "toLowerCase", "substring", "lastIndex", "startsWith", "_objectToURLParamsArray", "obj", "params", "push", "encodeURIComponent", "e", "parseStandardURIString", "str", "toString", "_standardURIToString", "pathname", "replace", "authority", "userinfoEndIndex", "indexOf", "host", "portBeginIndex", "lastIndexOf", "port", "hostname", "hashBeginIndex", "search", "hash", "thiz", "urlObjectToString", "o", "tmp", "serverURL", "room", "URL", "url", "scheme", "endsWith", "domain", "contextRoot", "roomName", "jwt", "lang", "release", "URLSearchParams", "set", "defaultLanguage", "configOverwrite", "searchString", "urlPrefix", "urlParamsArray", "urlParamsString", "join", "undefined", "DEFAULT_POSTIS_OPTIONS", "opener", "parent", "POSTIS_METHOD_NAME", "PostMessageTransportBackend", "postisOptions", "postis", "options", "readynessCheck", "scope", "targetWindow", "windowForEventListening", "<PERSON><PERSON><PERSON><PERSON>", "listeners", "send<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ready", "readyMethod", "listener", "event", "data", "parse", "origin", "listenersForMethod", "method", "call", "addEventListener", "listen", "callback", "listenBufferForMethod", "j", "send", "opts", "postMessage", "setTimeout", "destroy", "clearInterval", "removeEventListener", "readyCheckID", "Date", "Math", "random", "setInterval", "id", "Post<PERSON>", "_receiveCallback", "message", "dispose", "setReceiveCallback", "MESSAGE_TYPE_REQUEST", "MESSAGE_TYPE_RESPONSE", "Transport", "backend", "_listeners", "Map", "_requestID", "_responseHandlers", "_unprocessedMessages", "Set", "addListener", "on", "setBackend", "_disposeBackend", "_backend", "_onMessageReceived", "type", "handler", "get", "delete", "result", "error", "removeAllListeners", "eventName", "args", "listenersForEvent", "isProcessed", "size", "add", "removeListener", "sendEvent", "sendRequest", "request", "Promise", "reject", "Error", "resolve", "bind", "dont<PERSON><PERSON><PERSON>", "source", "paramStr", "paramParts", "substr", "split", "firstParam", "part", "param", "some", "k", "value", "decoded", "decodeURIComponent", "msg", "onerror", "reportError", "String", "parseURLParams", "location", "jitsi_meet_external_api_id", "JitsiMeetJS", "app", "setExternalTransportBackend", "externalTransportBackend", "transport", "logger", "<PERSON><PERSON>", "_setDevice", "device", "name", "ALWAYS_ON_TOP_FILENAMES", "commands", "addBreakoutRoom", "answerKnockingParticipant", "approveVideo", "askToUnmute", "autoAssignToBreakoutRooms", "avatarUrl", "cancelPrivateChat", "closeBreakoutRoom", "displayName", "endConference", "email", "grantModerator", "hangup", "hideNotification", "initiatePrivateChat", "joinBreakoutRoom", "localSubject", "kickParticipant", "muteEveryone", "overwriteConfig", "overwriteNames", "password", "pinParticipant", "rejectParticipant", "removeBreakoutRoom", "resizeFilmStrip", "resizeLargeVideo", "sendChatMessage", "sendEndpointTextMessage", "sendParticipantToRoom", "sendTones", "setAssumedBandwidthBps", "setFollowMe", "setLargeVideoParticipant", "setMediaEncryptionKey", "setNoiseSuppressionEnabled", "setParticipantVolume", "setSubtitles", "setTileView", "setVideoQuality", "showNotification", "startRecording", "startShareVideo", "stopRecording", "stopShareVideo", "subject", "submitFeedback", "toggleAudio", "toggleCamera", "toggleCameraMirror", "toggleChat", "toggleE2EE", "toggleFilmStrip", "toggle<PERSON><PERSON><PERSON>", "toggleModeration", "toggleNoiseSuppression", "toggleParticipantsPane", "toggleRaiseHand", "toggleShareScreen", "toggleSubtitles", "toggleTile<PERSON>iew", "toggleVirtualBackgroundDialog", "toggleVideo", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "events", "changeParticipantNumber", "APIInstance", "number", "_numberOfParticipants", "parseSizeParam", "parsedValue", "JitsiMeetExternalAPI", "width", "height", "parentNode", "document", "body", "interfaceConfigOverwrite", "onload", "invitees", "devices", "userInfo", "e2eeKey", "sandbox", "parseArguments", "_parentNode", "_url", "generateURL", "appData", "_createIFrame", "_transport", "_frame", "contentWindow", "Array", "isArray", "invite", "_tmpE2EE<PERSON>ey", "_isLargeVideoVisible", "_isPrejoinVideoVisible", "_participants", "_myUserID", "_onStageParticipant", "_setupListeners", "frameName", "createElement", "allow", "_setSize", "setAttribute", "style", "border", "src", "append<PERSON><PERSON><PERSON>", "_getAlwaysOnTopResources", "iframeWindow", "iframeDocument", "baseURL", "base", "querySelector", "href", "map", "filename", "_getFormattedDisplayName", "participantId", "formattedDisplayName", "_getOnStageParticipant", "_getLargeVideo", "iframe", "getIFrame", "getElementById", "_getPrejoinVideo", "_getParticipantVideo", "parsedHeight", "parsedWidth", "_height", "_width", "userID", "hexToBytes", "hex", "bytes", "c", "parseInt", "executeCommand", "exportedKey", "index", "avatarURL", "user", "displayname", "isVisible", "_videoQuality", "videoQuality", "updateNumberOfParticipants", "rooms", "allParticipants", "reduce", "prev", "roomItemKey", "participants", "isP2pActive", "addEventListeners", "captureLargeVideoScreenshot", "<PERSON><PERSON><PERSON><PERSON>", "executeCommands", "commandList", "getAvailableDevices", "catch", "getContentSharingParticipants", "getCurrentDevices", "getCustomAvatarBackgrounds", "getLivestreamUrl", "getParticipantsInfo", "participantIds", "participantsInfo", "values", "participant", "idx", "getVideoQuality", "isAudioAvailable", "isDeviceChangeAvailable", "deviceType", "isDeviceListAvailable", "isMultipleAudioInputSupported", "TypeError", "isAudioMuted", "isAudioDisabled", "isModerationOn", "mediaType", "isParticipantForceMuted", "isParticipantsPaneOpen", "isSharingScreen", "isStartSilent", "getAvatarURL", "getDeploymentInfo", "getDisplayName", "getEmail", "getNumberOfParticipants", "isVideoAvailable", "isVideoMuted", "listBreakoutRooms", "videoType", "removeEventListeners", "eventList", "sendProxyConnectionEvent", "setAudioInputDevice", "label", "deviceId", "kind", "setAudioOutputDevice", "setVideoInputDevice", "mode", "enabled", "keyInfo", "crypto", "subtle", "exportKey", "from", "Uint8Array", "internals", "text", "firstOptions", "reviver", "protoAction", "scan", "next", "nodes", "node", "prototype", "hasOwnProperty", "SyntaxError", "__proto__", "safeParse", "ignoreError", "require", "LogCollector", "logStorage", "stringifyObjects", "storeInterval", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "levels", "logLevel", "_log", "apply", "arguments", "storeLogsIntervalID", "queue", "totalLen", "outputCache", "someObject", "formatLogMessage", "len", "arg", "ERROR", "timestamp", "prevMessage", "prevMessageText", "count", "_flush", "start", "_reschedulePublishInterval", "clearTimeout", "flush", "force", "reschedule", "isReady", "cachedQueue", "storeLogs", "stop", "consoleTransport", "globalTransports", "addGlobalTransport", "removeGlobalTransport", "transportIdx", "splice", "globalOptions", "getCallerInfo", "callerInfo", "methodName", "fileLocation", "line", "column", "stack", "m", "log", "level", "slice", "disableCallerInfo", "transports", "concat", "t", "l", "logPrefixes", "toISOString", "fullLogParts", "methods", "setGlobalOptions", "setLevel", "TRACE", "DEBUG", "INFO", "LOG", "WARN", "idLoggers", "loggers", "curLevel", "<PERSON><PERSON><PERSON><PERSON>", "setLogLevelById", "setLogLevel", "ReflectOwnKeys", "R", "Reflect", "ReflectApply", "target", "receiver", "Function", "ownKeys", "getOwnPropertySymbols", "getOwnPropertyNames", "NumberIsNaN", "Number", "isNaN", "init", "once", "emitter", "errorListener", "err", "resolver", "eventTargetAgnosticAddListener", "flags", "addErrorHandlerIfEventEmitter", "_events", "_eventsCount", "_maxListeners", "defaultMaxListeners", "checkListener", "_getMaxListeners", "that", "_addListener", "prepend", "existing", "warning", "create", "newListener", "unshift", "warned", "w", "onceWrapper", "fired", "wrapFn", "_onceWrap", "state", "wrapped", "unwrap", "evlistener", "arr", "ret", "unwrapListeners", "arrayClone", "listenerCount", "copy", "wrapListener", "defineProperty", "enumerable", "RangeError", "getPrototypeOf", "setMaxListeners", "getMaxListeners", "do<PERSON><PERSON><PERSON>", "er", "context", "prependListener", "prependOnceListener", "list", "position", "originalListener", "shift", "pop", "spliceOne", "off", "rawListeners", "eventNames", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "__esModule", "d", "a", "definition", "prop"], "sourceRoot": ""}
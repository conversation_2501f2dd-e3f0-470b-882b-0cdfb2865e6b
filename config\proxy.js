/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

// const targetUrl = 'http://36.137.22.198:9000/serv/' // 演示环境
// const targetUrl = 'http://125.77.202.162:24174/serv/'
const targetUrl = 'http://192.168.16.39:9000/serv/'
// const targetUrl = 'http://127.0.0.1:9999' // 本地
// const targetUrl = 'http://218.85.131.35:9000/serv' // 测试环境
// const targetUrl = 'http://218.85.131.35:13000/serv' // 外网环境
// const targetUrl = 'http://192.168.15.73:24271' // 吴昊
// const targetUrl = 'http://192.168.15.122:24320' //伟波
// const targetUrl = 'http://192.168.15.74:9999' // 景城
// const targetUrl = 'http://192.168.15.72:9999' // 叶亦添

export default {
  dev: {
    // ============ 直连服务方式开启 ============
    // '/user/open': {
    //   target: 'http://127.0.0.1:7777/',
    //   changeOrigin: true,
    //   pathRewrite: { '^/user': '' },
    // },
    // '/user/api': {
    //   target: 'http://127.0.0.1:7777/',
    //   changeOrigin: true,
    //   pathRewrite: { '^/user': '' },
    // },
    // '/publicAlarm': {
    //   target: 'http://127.0.0.1:8011/',
    //   changeOrigin: true,
    //   pathRewrite: { '^/publicAlarm': '' },
    // },
    // '/disposal': {
    //   target: 'http://127.0.0.1:8012/',
    //   changeOrigin: true,
    // },
    // ============ 走网关方式开启 ============
    '/user/open': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/user/api': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/publicAlarm': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/irda-alarm': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/disposal': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/alarmSocket': {
      target: targetUrl,
      changeOrigin: true,
      ws: true,
    },
    '/video': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/message': {
      target: targetUrl,
      changeOrigin: true,
      ws: true,
    },
    '/workflow': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/audit': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/education': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/irda-exam': {
      target: targetUrl,
      changeOrigin: true,
    },
    '/amap': {
      target: 'https://tm.amap.com',
      changeOrigin: true,
      pathRewrite: {
        '/amap': '',
      },
    },
    '/arcgisServer': {
      target: 'http://183.251.102.61:8000/',
      changeOrigin: true,
      pathRewrite: {
        '/arcgisServer': '',
      },
    },
    '/arcgis/rest': {
      target: 'http://183.251.102.75:8000/',
      changeOrigin: true,
    },
  },
};

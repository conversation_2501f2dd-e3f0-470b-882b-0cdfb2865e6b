import React, { useEffect, useRef, useState } from 'react';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import SceneView from '@arcgis/core/views/SceneView';
import MapView from '@arcgis/core/views/MapView';
import { tdtLayer } from './tdt';
import Toolbar from '@/components/Arcgis4/toolbar';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import HeatmapRenderer from '@arcgis/core/renderers/HeatmapRenderer.js';
import Graphic from '@arcgis/core/Graphic';
import Point from '@arcgis/core/geometry/Point';
import SceneLayer from '@arcgis/core/layers/SceneLayer';
import LabelClass from '@arcgis/core/layers/support/LabelClass';
import { connect } from 'umi';
import Circle from '@arcgis/core/geometry/Circle';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import WebTileLayer from '@arcgis/core/layers/WebTileLayer';
import md5 from 'js-md5';
import { HIGH_LAYER_ID, LAYER_TYPE, MAP_GRID_OPTION, ROAD_STATUS } from '@/constants/mapConstants';
import { arcGISJSPath, arcGISPath, getResUrl } from '@/constants/global';
import MapImageLayer from '@arcgis/core/layers/MapImageLayer';
import { Polygon } from '@arcgis/core/geometry';
import TextSymbol from '@arcgis/core/symbols/TextSymbol';
import Color from '@arcgis/core/Color';
import Font from '@arcgis/core/symbols/Font';
import Extent from '@arcgis/core/geometry/Extent';
import SimpleFillSymbol from '@arcgis/core/symbols/SimpleFillSymbol';
import SimpleLineSymbol from '@arcgis/core/symbols/SimpleLineSymbol';
import WMSLayer from '@arcgis/core/layers/WMSLayer';
import SpatialReference from '@arcgis/core/geometry/SpatialReference';
import { differenceWith, isEqual } from 'lodash';
import { message } from 'antd';

import stylesIdx from './index.less';
import '@arcgis/core/assets/esri/themes/light/main.css';
import '@arcgis/core/assets/esri/themes/light/view.css';

import defaultIcon from '@/assets/common/icon_locate_alarm.svg';
import uavIcon from '@/assets/common/icon_uav.png';

const layerTypes = [
  'layer1',
  'layer2',
  'layer3',
  'layer4',
  'layer5',
  'layer6',
  'layer7',
  'traffic',
  'layer10',
  'layer11',
  'layer12',
  'layer13',
  'layer14',
  'layer15',
  'layerWMS',
  'layerWMTS',
];
const BASE_LAYER_ID = ['layer1', 'layer2', 'layer3', 'layer4', 'layer5', 'layer6', 'layer7'];
let xm3DLaysers = [];
const xmBaseLayers = {
  vec: `${arcGISPath}arcgis/rest/services/HB/CGCS_XMMAP/MapServer`,
  cva: `${arcGISPath}arcgis/rest/services/HB/CGCS_XMMAP_CVA/MapServer`,
  img: `${arcGISPath}arcgis/rest/services/HB/CGCS_DOMMAP/MapServer`,
};
// const elevationLayerUrl = `${arcGISPath}arcgis/rest/services/BaseMap/dem2015_3857/ImageServer`
const elevationLayerUrl = '';

function ArcgisMap(props) {
  const {
    id = 'map',
    option, // 地图参数 center、zoom
    sceneView = false, //是否三维地图
    data = [], //地图数据
    onClick, //点击图标事件
    addInformationPoint,
    dispatch,
    hideToolbar = false, //隐藏工具栏
    height = 'calc(100vh)', //地图视图高度
    arcgis: { toolbarLayers },
    setMapAndView,
    showDrawing = false,
    zIndex,
    FAM_LAYER_3D = false, //权限三维图层
    clickType = [],
    closeMapDraw, // 清空画图对象
    extentInfo = {},
    clickDraw,
    onMapClick = () => {}, // 地图点击事件
  } = props;
  const [initMapData, setInitMapData] = useState({});
  const [map, setMap] = useState(null);
  const [view, setView] = useState(null);
  const [view3D, setView3D] = useState(false);
  const [isInit, setIsInit] = useState(false);
  const [isDraw, setIsDraw] = useState(false);
  const [baseLayerInfo, setBaseLayerInfo] = useState({
    layer1: false,
    layer2: false,
    layer3: false,
    layer4: false,
    layer5: false,
    layer6: false,
    layer7: false,
  });
  const [showImage, setShowImage] = useState(
    localStorage.getItem('mapDefaultSatelliteLayer') === '1',
  );
  const mapDiv = useRef();

  /**
   * 组件初始化加载初始经纬度
   */
  useEffect(() => {
    loadInitLocation();
  }, []);

  const loadInitLocation = () => {
    dispatch({
      type: 'util/getByCode',
      payload: {
        code: 'location',
      },
    }).then((resp) => {
      if (resp.code == '00') {
        let _initMapData = JSON.parse(resp.data.dictValue);
        setInitMapData(_initMapData);
      } else {
        message.warn('加载地图初始位置出错，请检查数据库').then();
      }
    });
  };

  useEffect(() => {
    if (initMapData.lon && initMapData.lat) {
      loadMap(sceneView);
    }
  }, [initMapData.lon, initMapData.lat]);

  /**
   * 数据发生改变时 加载数据
   */
  useEffect(() => {
    loadData();
  }, [data]);

  /**
   * 视图发生改变时 加载数据
   */
  useEffect(() => {
    if (isInit) {
      loadData();
    }
  }, [view, isInit]);

  useEffect(() => {
    if (view && extentInfo.ymax) {
      setCenter();
    }
  }, [extentInfo]);

  /**
   * 更改地图配置  zoom+center
   */
  useEffect(() => {
    if (view) {
      let point;
      if (option.center.length === 2) {
        point = new Point({
          longitude: option.center[0],
          latitude: option.center[1],
          spatialReference: view.spatialReference,
        });
      } else {
        point = new Point({
          longitude: view.center[0],
          latitude: view.center[1],
          spatialReference: view.spatialReference,
        });
      }
      view.goTo({
        target: point,
        zoom: option?.zoom ? (view3D ? option.zoom + 5 : option.zoom) : view.zoom,
      });
    }
  }, [option]);

  // const ExaggeratedElevationLayer = BaseElevationLayer.createSubclass({
  //   load: function () {
  //     this._elevation = new ElevationLayer({
  //       url: elevationLayerUrl,
  //     });
  //     this.addResolvingPromise(
  //       this._elevation.load().then(() => {
  //         this.tileInfo = this._elevation.tileInfo;
  //         this.spatialReference = this._elevation.spatialReference;
  //         this.fullExtent = this._elevation.fullExtent;
  //       }),
  //     );
  //     return this;
  //   },
  //   fetchTile: function (level, row, col, options) {
  //     return this._elevation.fetchTile(level, row, col, options).then(
  //       function (data) {
  //         return data;
  //       }.bind(this),
  //     );
  //   },
  // });

  /**
   * 初始化地图
   */
  const loadMap = async (flag3D) => {
    const { lon, lat, defaultZoom } = initMapData;
    if (view) {
      view.destroy();
    }
    esriConfig.fontsUrl = getResUrl(`${arcGISJSPath}/arcgisFont`);
    esriConfig.apiKey = '6facb09a0103c251a25f44cd6f5f9b88503f30b335d009f9a64cda07cc056ed5';
    esriConfig.portalUrl = getResUrl(arcGISJSPath);
    esriConfig.assetsPath = getResUrl(`${arcGISJSPath}/@arcgis/core/assets`);
    const threeD = flag3D || sceneView;
    const m = new Map({
      // ground: "world-elevation",
      // ground: {
      //   layers: [elevationLayer]
      // }
    });
    let tempView = null;
    if (threeD) {
      tempView = new SceneView({
        container: id,
        map: m,
        // zoom: option?.zoom ? option?.zoom + 5 : 13,
        zoom: defaultZoom,
        center: option?.center || [lon, lat], //120.377531,36.066661
      });
      tempView.ui.components = [];
      // 设置最小、最大层级
      const maxZoom = 20;
      const minZoom = 5;
      tempView.watch('zoom', function (value) {
        if (value > maxZoom) {
          tempView.zoom = maxZoom;
        }
        if (value < minZoom) {
          tempView.zoom = minZoom;
        }
      });
    } else {
      tempView = new MapView({
        container: id,
        map: m,
        // zoom: option?.zoom || 8,
        zoom: option?.zoom || defaultZoom,
        center: option?.center || [lon, lat], 
        constraints: {
          // lods: lods,
          maxZoom: 18,
          minZoom: 4,
        },
      });
      tempView.ui.components = [];
    }
    if (id === 'map') {
      await dispatch({
        type: 'arcgis/putData',
        payload: {
          map: m,
          view: tempView,
        },
      });
    } else {
      setIsInit(true);
    }

    addTdtLayers(m);
    addWmsLayer(m);
    // addWmtsLayer(m);// 去除正射影像
    addExtentGraphics(tempView);
    // if (threeD && arcGisXMVIEW) {
    //   addXMLayers(m);
    // }
    setView(tempView);
    setView3D(threeD);
    setMap(m);
    setMapAndView(m, tempView);
    if (onClick) {
      clickEvent(tempView);
    }
  };

  /**
   * 限制地图可拖动范围
   * @param event 鼠标事件
   */
  const limitExtentChange = (event, type) => {
    if (event !== null) {
      event.stopPropagation();
    }
    const { xmin, ymin, xmax, ymax, expand } = initMapData;
    // 定义允许的地图范围
    const initExtent = new Extent({
      xmin: xmin,
      ymin: ymin,
      xmax: xmax,
      ymax: ymax,
      spatialReference: new SpatialReference({ wkid: 4326 }),
    });
    const allowExtent = initExtent.expand(expand);

    let nowExtent = view.extent;
    if (nowExtent !== null) {
      if (type === 'drag') {
        if (
          nowExtent.xmin < allowExtent.xmin ||
          nowExtent.ymin < allowExtent.ymin ||
          nowExtent.xmax > allowExtent.xmax ||
          nowExtent.ymax > allowExtent.ymax
        ) {
          view.extent = allowExtent;
        }
      } else if (type === 'zoom') {
        if (
          (nowExtent.xmin < allowExtent.xmin && nowExtent.ymin < allowExtent.ymin) ||
          (nowExtent.xmax > allowExtent.xmax && nowExtent.ymax > allowExtent.ymax)
        ) {
          view.extent = allowExtent;
        }
      }
    }
  };

  useEffect(() => {
    if (view === null) {
      return;
    }
    // 去除正射影像
    
    // // 监听拖动和缩放事件
    // view.on(['drag', 'mouse-wheel'], function (event) {
    //   if (event.type === 'drag' && event.action === 'end') {
    //     // 拖动地图时，限制地图范围
    //     limitExtentChange(event, 'drag');
    //   }
    // });
    // // 缩放地图时，限制地图范围
    // view.watch('zoom', (newZoom, oldZoom) => {
    //   // console.log('Zoom changed from', oldZoom, 'to', newZoom);
    //   limitExtentChange(null, 'zoom');
    // });
  }, [view]);

  useEffect(() => {
    let flag = true;
    for (const key in baseLayerInfo) {
      if (!baseLayerInfo[key]) {
        flag = false;
        break;
      }
    }
    setIsInit(flag);
  }, [baseLayerInfo]);

  /**
   * 地图点击事件
   * @param v 地图视图
   */
  const clickEvent = (v) => {
    v.when(function () {
      // 点击事件
      v.popup.autoOpenEnabled = false;
      v.on('click', function (event) {
        v.hitTest(event).then(function (response) {
          const { results = [] } = response;
          onMapClick && onMapClick(event);

          if (results.length <= 0 && addInformationPoint) {
            if (event.button === 0) {
              addInformationPoint(event);
            }
          }
          for (let i = 0; i < results.length; i++) {
            const result = results[i];
            const { graphic } = result;
            const {
              geometry: { type },
              attributes,
            } = graphic;
            if (!clickType.length || clickType.indexOf(type) > -1) {
              if (attributes) {
                onClick(
                  v,
                  event,
                  attributes.id,
                  attributes.type,
                  attributes.data,
                  attributes.longitude,
                  attributes.latitude,
                  graphic,
                );
              }
              break;
            }
          }
        });
      });
      v.on('layerview-create', function (event) {
        if (BASE_LAYER_ID.includes(event.layer.id)) {
          setBaseLayerInfo((prevState) => {
            prevState[event.layer.id] = true;
            return { ...prevState };
          });
        }
      });
    });
  };

  /**
   * 增加厦门三维地图
   */
  const addXMLayers = (m) => {
    xm3DLaysers.forEach((url, index) => {
      const sceneLayer = new SceneLayer({
        id: url,
        url: url,
        popupEnabled: false,
      });
      m.add(sceneLayer);
    });
  };

  const setCenter = () => {
    if (view && extentInfo.ymax) {
      view.extent = new Extent({
        xmin: extentInfo.xmin,
        ymin: extentInfo.ymin,
        xmax: extentInfo.xmax,
        ymax: extentInfo.ymax,
        spatialReference: { wkid: 4326 },
      }).expand(0.72);
    }
  };
  // 加载热力图
  const loadHeatmap = (itemData) => {
    const {
      id,
      data = [],
      colorStops = [
        { ratio: 0, color: 'rgba(0, 255, 255, 0)' },
        { ratio: 0.3, color: 'rgba(0, 255, 0, 0.5)' },
        { ratio: 0.6, color: 'rgba(255, 255, 0, 0.8)' },
        { ratio: 1, color: 'rgba(255, 0, 0, 1)' },
      ],
      fields = [{ name: 'magnitude', type: 'double' }],
      blurRadius = 18
    } = itemData;
    const graphics = data.map((item) => {
        return new Graphic({
          geometry: {
            type: 'point',
            longitude: item.lon,
            latitude: item.lat,
          },
          attributes: {
            magnitude: item.num, // 确保字段名与渲染器配置一致
          },
        });
      });
      const heatmapRenderer = new HeatmapRenderer({
        field: 'magnitude',
        colorStops,
        blurRadius,
        maxDensity: 1,
        minDensity: 0
      });
      const heatmapLayer = new FeatureLayer({
        id,
        source: graphics, // 动态数据源
        objectIdField: 'id', // 唯一标识字段
        fields,
        renderer: heatmapRenderer,
        spatialReference: view.spatialReference, // 指定坐标系（与数据一致）
      });
      map.add(heatmapLayer);
  }

  /**
   * 地图数据加载
   */
  const loadData = () => {
    if (data && map) {
      // 如果有3D图层，则底图自动切换为3D底图
      const scene = data.filter((item) => item.type === 'SceneLayer');
      if (scene.length > 0 && !view3D) {
        changeView(!view3D);
      }
      // 加载图层数据
      data.forEach((item) => {
        const { type } = item;
        // 点标记
        if (type === LAYER_TYPE.FeatureLayer) {
          loadFeatureLayer(item);
          return;
        }
        if (type === LAYER_TYPE.GraphicsLayer) {
          loadGraphicsLayer(item);
          return;
        }
        // 三维图层
        if (type === 'SceneLayer') {
          loadSceneLayer(item);
          return;
        }
        // 前缀三维图层
        if (type === LAYER_TYPE.CustomSceneLayer) {
          loadCustomSceneLayer(item);
          return;
        }
        // 缓冲区
        if (type === LAYER_TYPE.Buffer) {
          loadBuffer(item);
          return;
        }
        // 长方形
        if (type === LAYER_TYPE.Rectangle) {
          loadRectangle(item);
          return;
        }
        // 网格
        if (type === LAYER_TYPE.Grid) {
          loadGrid(item);
          return;
        }
        if (type === LAYER_TYPE.GridShowLayer) {
          loadGridList(item);
          return;
        }
        if (type === LAYER_TYPE.PolylineLayer) {
          loadPolyline(item);
          return;
        }
        if (type === LAYER_TYPE.MapImageLayer) {
          loadMapImageLayer(item);
          return;
        }
        if (type === LAYER_TYPE.Location) {
          loadLocation(item);
          return;
        }
        if (type === LAYER_TYPE.UAVPolylineLayer) {
          loadUAVPolyline(item);
          return;
        }
        // 热力图
        if (type === LAYER_TYPE.HeatMap) {
          loadHeatmap(item);
          return;
        }
      });
      const dataIds = data.map((item) => item.id);
      const toolIds = toolbarLayers.map((item) => item.id);
      const all = map.layers.filter((item) => layerTypes.indexOf(item.id) < 0);
      all.forEach((item) => {
        if (dataIds?.indexOf(item.id) < 0 && toolIds?.indexOf(item.id) < 0) {
          map.remove(item);
        }
      });
      const ids = [];
      for (let i = 0; i < map.layers.items.length; i++) {
        const lay = map.layers.items[i];
        if (ids.indexOf(lay.id) >= 0) {
          break;
        }
        if (HIGH_LAYER_ID[lay.id]) {
          map.reorder(lay, HIGH_LAYER_ID[lay.id]);
          i--;
        } else if (lay.type === 'feature') {
          map.reorder(lay, 10086);
          i--;
        }
        ids.push(lay.id);
      }
    }
  };

  /**
   * 2D、3D切换
   * @param flag
   */
  const changeView = (flag) => {
    props.getMsg(flag);
    setBaseLayerInfo({
      layer1: false,
      layer2: false,
      layer3: false,
      layer4: false,
      // layer5: false,
      // layer6: false,
      // layer7: false,
    });
    loadMap(flag);
  };

  /**
   * 增加天地图图层
   */
  const addTdtLayers = (m) => {
    const layer1 = tdtLayer('vec');
    const layer2 = tdtLayer('cva');
    const layer3 = tdtLayer('img');
    const layer4 = tdtLayer('cia');
    layer1.id = 'layer1';
    layer2.id = 'layer2';
    layer3.id = 'layer3';
    layer4.id = 'layer4';
    m.layers.addMany([layer3, layer4, layer1, layer2]);
    layer1.visible = !showImage;
    layer2.visible = !showImage;
    layer3.visible = showImage;
    layer4.visible = showImage;
  };

  /**
   * 添加 WMS 图层
   */
  const addWmsLayer = (m) => {
    // 添加美兰道路WMS服务
    const roadWmsLayer = new WMSLayer({
      id: 'layerWMS',
      url: 'http://192.168.15.204:8066/geoserver/ml/wms',
      sublayers: [{ name: 'ml:road' }],
      visible: true,
      spatialReference: { wkid: 4326 },
      imageFormat: 'image/png',
    });
    m.layers.add(roadWmsLayer);
  };

  /**
   * 添加 wmts缓存切片 图层
   */
  const addWmtsLayer = (m) => {
    const { wmtsUrl, wmsLyr } = initMapData;
    // 创建WMTS图层
    const wmtsLayer = new WebTileLayer({
      id: 'layerWMTS',
      // http://192.168.15.204:8066/geoserver/hrb/gwc/service/wmts?LAYER=hrb:hrbtiff
      urlTemplate:
        wmtsUrl +
        '?LAYER=' +
        wmsLyr +
        '&STYLE=&TILEMATRIXSET=EPSG:4326&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&FORMAT=image/png&TILEMATRIX=EPSG:4326:{level}&TILEROW={row}&TILECOL={col}',
      spatialReference: new SpatialReference({
        wkid: 4326,
      }),
      tileInfo: {
        dpi: 96,
        format: 'png',
        height: 256,
        width: 256,
        lods: [
          { level: 0, resolution: 0.703125, scale: 295497593.05875 },
          { level: 1, resolution: 0.3515625, scale: 147748796.529375 },
          { level: 2, resolution: 0.17578125, scale: 73874398.264688 },
          { level: 3, resolution: 0.087890625, scale: 36937199.132344 },
          { level: 4, resolution: 0.0439453125, scale: 18468599.566172 },
          { level: 5, resolution: 0.02197265625, scale: 9234299.783086 },
          { level: 6, resolution: 0.010986328125, scale: 4617149.891543 },
          { level: 7, resolution: 0.0054931640625, scale: 2308574.945771 },
          { level: 8, resolution: 0.00274658203125, scale: 1154287.472886 },
          { level: 9, resolution: 0.001373291015625, scale: 577143.736443 },
          { level: 10, resolution: 0.0006866455078125, scale: 288571.868221 },
          { level: 11, resolution: 0.00034332275390625, scale: 144285.934111 },
          { level: 12, resolution: 0.000171661376953125, scale: 72142.967056 },
          { level: 13, resolution: 8.58306884765625e-5, scale: 36071.483528 },
          { level: 14, resolution: 4.291534423828125e-5, scale: 18035.741764 },
          { level: 15, resolution: 2.1457672119140625e-5, scale: 9017.870882 },
          { level: 16, resolution: 1.0728836059570313e-5, scale: 4508.935441 },
          { level: 17, resolution: 5.3644180297851563e-6, scale: 2254.467721 },
          { level: 18, resolution: 2.6822090148925782e-6, scale: 1127.233861 },
          { level: 19, resolution: 1.3411045074462891e-6, scale: 563.6169305 },
          { level: 20, resolution: 6.7055225372314453e-7, scale: 281.80846525 },
        ],
        origin: {
          x: -180,
          y: 90,
        },
        spatialReference: {
          wkid: 4326,
        },
        tileMatrixSetId: 'EPSG:4326',
      },
    });
    m.layers.add(wmtsLayer);
  };

  /**
   * 添加 extent 边界
   * @param {*} tempView
   */
  const addExtentGraphics = (tempView) => {
    const { xmin, ymin, xmax, ymax, showExtent } = initMapData;

    if (!showExtent) {
      return;
    }
    // 创建一个 Polygon 几何对象
    const polygon = new Polygon({
      rings: [
        [xmin, ymin],
        [xmax, ymin],
        [xmax, ymax],
        [xmin, ymax],
        [xmin, ymin],
      ],
      spatialReference: new SpatialReference({ wkid: 4326 }),
    });

    // 创建一个 SimpleLineSymbol
    const lineSymbol = new SimpleLineSymbol({
      color: [255, 0, 0], // 红色
      width: 2,
      style: 'solid',
    });

    // 创建一个 Graphic 对象
    const graphic = new Graphic({
      geometry: polygon,
      symbol: lineSymbol,
    });

    // 将 Graphic 添加到地图的某个图层中
    tempView.graphics.add(graphic);
  };

  /**
   * 加载自建底图
   */
  const addMyLayers = (m) => {
    const layer5 = new MapImageLayer({
      url: xmBaseLayers.vec,
    });
    const layer6 = new MapImageLayer({
      url: xmBaseLayers.cva,
    });
    const layer7 = new MapImageLayer({
      url: xmBaseLayers.img,
    });
    layer5.id = 'layer5';
    layer6.id = 'layer6';
    layer7.id = 'layer7';
    m.layers.addMany([layer7, layer5, layer6]);
    layer7.visible = false;
  };

  const loadLocation = (data) => {
    const {
      id,
      iconList = [],
      indexColumn,
      title,
      imageWidth = 24,
      imageHeight = 24,
      showIcon = true,
      showTitle = false,
      points = [],
      image3D = '',
      materialColor = '',
      textSymbol: {
        color = 'white',
        // haloColor = 'black',
        haloColor = '#ffb3b3',
        haloSize = '1px',
        xoffset = 0,
        yoffset = 12,
        font: { size = 8, weight = 'normal' },
        ...rest
      },
    } = data;

    let layer = map.findLayerById(id);
    if (!layer) {
      layer = new GraphicsLayer({
        id,
        title,
      });
      let graphics = [];
      points.map((item) => {
        const { longitude, latitude, text } = item;
        if (showIcon) {
          let symbol = {
            type: 'picture-marker',
            url: iconList[item[indexColumn]],
            width: imageWidth,
            height: imageHeight,
            // angle:-270  //2D 转向支持angle属性 -270
          };
          if (view3D && image3D) {
            symbol = {
              type: 'point-3d',
              symbolLayers: [
                {
                  // heading:-180, //3D 转向支持heading属性
                  type: 'object',
                  resource: {
                    href: image3D,
                  },
                  material: { color: materialColor },
                  width: 10,
                },
              ],
            };
          }
          const graphic = new Graphic({
            geometry: {
              type: 'point',
              longitude,
              latitude,
              spatialReference: view.spatialReference,
            },
            symbol,
            attributes: item,
          });
          graphics.push(graphic);
        }
        if (showTitle) {
          const graphic = new Graphic({
            geometry: {
              type: 'point',
              longitude,
              latitude,
              spatialReference: view.spatialReference,
            },
            symbol: {
              type: view3D ? 'label-3d' : 'text',
              text,
              color,
              haloColor,
              haloSize,
              xoffset,
              yoffset,
              font: {
                size,
                weight,
              },
              ...rest,
            },
            attributes: item,
          });
          graphics.push(graphic);
        }
      });
      layer.graphics.addMany(graphics);
      map.add(layer);
      map.reorder(layer, 9999);
    } else {
      const { graphics = [] } = layer;
      let saveIdList = [];
      const oldList = [...graphics];
      oldList.map((item) => {
        const { attributes } = item;
        const obj = differenceWith([attributes], points, isEqual);
        const { id } = attributes;
        if (obj.length) {
          layer.graphics.remove(item);
        } else {
          saveIdList.push(id);
        }
      });
      let addGraphics = [];
      points.map((item) => {
        const { id, longitude, latitude, text } = item;
        if (saveIdList.includes(id)) return;
        let oldRecord = oldList.find((it) => it.id == id);
        let angle = getAngle(item, oldRecord);
        if (showIcon) {
          let symbol = {
            type: 'picture-marker',
            url: iconList[item[indexColumn]],
            width: imageWidth,
            height: imageHeight,
            // angle:angle-270  //2D 转向支持angle属性 为零时朝向西方 -270
          };
          if (view3D && image3D) {
            symbol = {
              type: 'point-3d',
              symbolLayers: [
                {
                  // heading:angle-180,//未加载地形图，都用angle 为零时朝向南方 -180
                  type: 'object',
                  resource: {
                    href: getResUrl(image3D),
                  },
                  material: { color: materialColor },
                  width: 5,
                },
              ],
            };
          }
          const graphic = new Graphic({
            geometry: {
              type: 'point',
              longitude,
              latitude,
              spatialReference: view.spatialReference,
            },
            symbol,
            attributes: item,
          });
          graphics.push(graphic);
        }
        if (showTitle) {
          const graphic = new Graphic({
            geometry: {
              type: 'point',
              longitude,
              latitude,
              spatialReference: view.spatialReference,
            },
            symbol: {
              type: 'text',
              text,
              color,
              haloColor,
              haloSize,
              xoffset,
              yoffset,
              font: {
                size,
                weight,
              },
              ...rest,
            },
            attributes: item,
          });
          addGraphics.push(graphic);
        }
      });
      layer.graphics.addMany(addGraphics);
    }
  };

  const calcDistance = (lon1, lat1, lon2, lat2) => {
    let radlat1 = (lat1 * Math.PI) / 180.0;
    let radlat2 = (lat2 * Math.PI) / 180.0;
    let a = radlat1 - radlat2;
    let b = (lon1 * Math.PI) / 180.0 - (lon2 * Math.PI) / 180.0;
    let distance =
      2 *
      6378137.0 *
      Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radlat2) * Math.cos(radlat2) * Math.pow(Math.sin(b / 2), 2),
        ),
      );
    return distance;
  };

  // 计算偏北角，0指向正北，90指向正东，顺时针旋转
  const calcHeading = (lon1, lat1, lon2, lat2) => {
    let radlat1 = (lat1 * Math.PI) / 180.0;
    let radlat2 = (lat2 * Math.PI) / 180.0;
    let radlon1 = (lon1 * Math.PI) / 180.0;
    let radlon2 = (lon2 * Math.PI) / 180.0;
    let y = Math.sin(radlon2 - radlon1) * Math.cos(radlat2);
    let x =
      Math.cos(radlat1) * Math.sin(radlat2) -
      Math.sin(radlat1) * Math.cos(radlat2) * Math.cos(radlon2 - radlon1);
    let bearing = (Math.atan2(y, x) * 180.0) / Math.PI;
    return bearing < 0 ? bearing + 360.0 : bearing;
  };

  // 计算俯仰角， 90度时平行于水平面，0度时自上向下垂直俯视， 180度时自下向上仰视
  const calcTilt = (lon1, lat1, alt1, lon2, lat2, alt2) => {
    let distance = calcDistance(lon1, lat1, lon2, lat2);
    let angle = (Math.atan2(alt2 - alt1, distance) * 180.0) / Math.PI;
    let tilt = angle + 90;
    return tilt;
  };
  const getAngle = (record, oldRecord) => {
    if (oldRecord) {
      if (oldRecord.lon && oldRecord.lat && record.lon && record.lat) {
        let angle = calcHeading(oldRecord.lon, oldRecord.lat, record.lon, record.lat);
        return angle;
      }
      return 0;
    }
    return 0;
  };

  //计算两点间的角度方向，仰角，由于高程都为0，故仰角都是90度水平方向，
  //  let heading = calcHeading(startPoint[0], startPoint[1], endPoint[0], endPoint[1])
  //  let tilt = calcTilt(startPoint[0], startPoint[1], startPoint[2], endPoint[0], endPoint[1], endPoint[2])
  //  //设置车辆的位置
  //  let point = {
  //     type: 'point',
  //     x: startPoint[0],
  //     y: startPoint[1],
  //     z: startPoint[2]
  //    }
  //  treeGraphic.geometry = point;
  //  //角度
  //  treeSymbol.symbolLayers[0].heading = heading - 180;
  //  treeGraphic.symbol = treeSymbol;
  /**
   * 加载图层数据 点标记
   * @param record
   */
  const loadFeatureLayer = async (record) => {
    const { id, points, haloColor = '#ffb3b3' } = record;
    if (!id || !points || points.length === 0) {
      return;
    }
    let layer = map.findLayerById(id);
    let graphics = [];
    const renderer = loadRenderer(record);
    if (!layer) {
      layer = new FeatureLayer({
        id: record.id,
        title: md5(JSON.stringify(record)),
        source: [],
        fields: [
          { name: 'id', alias: 'id', type: 'string' },
          { name: 'title', alias: 'title', type: 'string' },
          { name: 'image', alias: 'image', type: 'string' },
          { name: 'longitude', alias: 'longitude', type: 'double' },
          { name: 'latitude', alias: 'latitude', type: 'double' },
          { name: 'data', alias: 'data', type: 'string' },
          { name: 'type', alias: 'type', type: 'string' },
        ],
        objectIdField: 'ObjectId',
        outFields: ['*'],
        geometryType: 'point',
        spatialReference: view.spatialReference,
      });
      if (record.minScale) {
        layer.minScale = record.minScale;
      }
      map.add(layer);
    } else {
      points.forEach((item) => {
        delete item['ObjectId'];
      });
      if (layer.title === md5(JSON.stringify(record))) {
        return;
      }
      layer.title = md5(JSON.stringify(record));
    }
    layer.renderer = renderer;
    layer.labelingInfo = new LabelClass({
      symbol: {
        type: view3D ? 'label-3d' : 'text',
        color: '#000000',
        font: {
          size: 10,
        },
        haloColor: haloColor,
        haloSize: 1,
      },
      labelPlacement: 'above-center',
      labelExpressionInfo: {
        expression: 'DefaultValue($feature.title, "")',
      },
    });
    for (let i = 0; i < points.length; i++) {
      let point = { ...points[i] }; // 图层节点
      if (view3D && point.image3D) {
        point.image = point.image3D;
      }
      const { longitude, latitude } = point; // 经纬度
      const graphic = new Graphic({
        geometry: new Point({
          longitude,
          latitude,
        }),
        attributes: point,
      });
      graphics.push(graphic);
    }

    const features = await layer.queryFeatures();
    await layer.applyEdits({ deleteFeatures: features.features });
    await layer.applyEdits({ addFeatures: graphics });
    return layer;
  };

  /**
   * 加载图层数据 点标记
   * @param record
   */
  const loadGraphicsLayer = async (record) => {
    const { id, width, height, points, showOrder = false } = record;

    if (!id || !points || points.length === 0) {
      return;
    }
    let layer = map.findLayerById(id);
    if (!layer) {
      layer = new GraphicsLayer({
        id: record.id,
        title: md5(JSON.stringify(record)),
        spatialReference: view.spatialReference,
      });
      if (record.minScale) {
        layer.minScale = record.minScale;
      }
      map.add(layer);
    } else {
      points.forEach((item) => {
        delete item['ObjectId'];
      });
      if (layer.title === md5(JSON.stringify(record))) {
        return;
      }
      layer.title = md5(JSON.stringify(record));
    }
    layer.removeAll();
    const graphics = [];
    for (let i = 0; i < points.length; i++) {
      let point = { ...points[i] }; // 图层节点
      const { longitude, latitude } = point; // 经纬度
      const pointGeometry = new Point({
        longitude,
        latitude,
      });
      const graphic = new Graphic({
        geometry: pointGeometry,
        attributes: point,
        symbol:
          view3D && point.image3D
            ? {
                type: 'point-3d',
                symbolLayers: [
                  {
                    type: 'object',
                    resource: {
                      href: point.image3D,
                    },
                    width: 5,
                    material: { color: 'red' },
                  },
                ],
              }
            : {
                type: 'picture-marker',
                url: point.image || defaultIcon,
                height: `${height}px`,
                width: `${width}px`,
              },
      });
      graphics.push(graphic);
      if (showOrder) {
        // 创建文本符号
        const textSymbol = new TextSymbol({
          color: 'white',
          text: (i + 1).toString(), // 序号从1开始
          font: {
            size: 9,
          },
          yoffset: 0, // 调整文本位置
        });
        const textGraphic = new Graphic({
          geometry: pointGeometry,
          attributes: point,
          symbol: textSymbol,
        });
        graphics.push(textGraphic);
      }
    }
    layer.graphics.addMany(graphics);
    return layer;
  };

  /**
   * 加载三维图层
   * @param record
   */
  const loadSceneLayer = (record) => {
    let layer = map.findLayerById(record.id);
    if (layer) {
      return;
    }
    const sceneLayer = new SceneLayer({
      id: record.id,
      url: record.url,
      popupEnabled: false,
    });
    map.add(sceneLayer);
  };

  /**
   * 加载三维图层
   * @param record
   */
  const loadCustomSceneLayer = (record) => {
    // 设置图层的海拔偏移
    const currentElevationInfo = {
      mode: 'relative-to-ground',
      offset: -33,
      unit: 'meters',
    };

    let layer = map.findLayerById(record.id);
    if (layer) {
      return;
    }
    const sceneLayer = new SceneLayer({
      id: record.id,
      url: `${arcGISPath.substring(0, arcGISPath.length - 1)}${record.url}`,
      popupEnabled: false,
    });
    // 如果是集美的道路和植被，设置偏移
    if (record.id == '_JM11_XZDL_3857_SLPK_0112' || record.id == '_JM11_XZZB_3857_SLPK_0112') {
      sceneLayer.elevationInfo = currentElevationInfo;
    }
    map.add(sceneLayer);
  };

  /**
   * 根据数据生成点标记渲染
   * @param record
   */
  const loadRenderer = (record) => {
    const { baseImage, baseImage3D, points } = record;
    const urls = [];
    urls.push(defaultIcon);
    if (baseImage && urls.indexOf(baseImage) < 0) {
      urls.push(baseImage);
    }
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      if (point.image && urls.indexOf(point.image) < 0) {
        urls.push(point.image);
      }
      if (view3D && point.image3D) {
        // point.image = point.image3D;
        if (urls.indexOf(point.image3D) < 0) {
          urls.push(point.image3D);
        }
      }
    }
    const width = record.imageWidth ? `${record.imageWidth}px` : '32px';
    const height = record.imageHeight ? `${record.imageHeight}px` : '32px';
    let defaultSymbol = {};
    if (baseImage3D && view3D) {
      defaultSymbol = {
        type: 'point-3d',
        symbolLayers: [
          {
            type: 'object',
            resource: {
              href: baseImage3D,
            },
          },
        ],
      };
    } else {
      defaultSymbol = {
        type: 'picture-marker',
        url: baseImage || defaultIcon,
        height: height,
        width: width,
      };
    }
    const renderer = {
      type: 'unique-value',
      field: 'image',
      defaultSymbol: defaultSymbol,
      uniqueValueInfos: urls.map((item) => {
        if (item.indexOf('.gltf') < 0) {
          return {
            value: item,
            symbol: {
              type: 'picture-marker',
              url: item,
              height: height,
              width: width,
            },
          };
        } else {
          return {
            value: item,
            symbol: {
              type: 'point-3d',
              symbolLayers: [
                {
                  type: 'object',
                  resource: {
                    href: item,
                  },
                  width: 5,
                  material: { color: 'red' },
                },
              ],
            },
          };
        }
      }),
    };
    return renderer;
  };

  const handleDraw = (value) => {
    clickDraw(value);
  };

  /**
   * 增加工具栏图层
   * @param layer
   * @returns {Promise<void>}
   */
  const addToolbarLayer = async (layer) => {
    toolbarLayers.push(layer);
    await dispatch({
      type: 'arcgis/putData',
      payload: {
        toolbarLayers: toolbarLayers,
      },
    });
  };

  /**
   * 加载默认缓冲区
   * @param record
   */
  const loadBuffer = (record) => {
    let layer = map.findLayerById(record.id);
    if (!layer) {
      layer = new GraphicsLayer();
      layer.id = record.id;
      map.layers.add(layer);
    } else {
      // 无数据变化，直接返回
      if (layer.title === md5(JSON.stringify(record))) {
        return;
      }
    }
    layer.removeAll();
    layer.title = md5(JSON.stringify(record));
    const point = new Point({
      longitude: record.longitude,
      latitude: record.latitude,
      spatialReference: view.spatialReference,
    });
    const radius = record.radius;
    const color = record.color || 'transparent'; //[255,95,2,0.2];
    const borderColor = record.borderColor || [238, 85, 85, 0.4];
    const image = record.image;
    if (image) {
      const symbol = new PictureMarkerSymbol({
        url: image,
        height: 24,
        width: 24,
      });
      const graphic = new Graphic({
        geometry: point,
        symbol: symbol,
      });
      layer.add(graphic);
    }
    radius.forEach((item) => {
      const circle = new Circle({
        center: point,
        radius: item * 1.11,
        radiusUnit: 'meters',
        spatialReference: view.spatialReference,
      });
      const graphic = new Graphic({
        geometry: circle,
        symbol: {
          type: 'simple-fill',
          color: color,
          style: 'solid',
          outline: {
            width: 1,
            color: borderColor,
          },
        },
      });
      layer.add(graphic);
    });
  };

  /**
   * 加载长方形
   * @param record
   */
  const loadRectangle = (record) => {
    let layer = map.findLayerById(record.id);
    if (!layer) {
      layer = new GraphicsLayer();
      layer.id = record.id;
      layer.title = md5(JSON.stringify(record));
    } else {
      // 无数据变化，直接返回
      if (layer.title === md5(JSON.stringify(record))) {
        return;
      }
    }
    layer.removeAll();
    if (record.id) {
      layer.id = record.id;
    }
    layer.title = md5(JSON.stringify(record));
    map.layers.add(layer);
    const polygon = {
      type: 'polygon',
      rings: record.rings || [],
      spatialReference: view.spatialReference,
    };
    const graphic = new Graphic({
      geometry: polygon,
      symbol: {
        type: 'simple-fill',
        color: [252, 41, 54, 0.05],
        style: 'solid',
        outline: {
          color: '#ff5502',
          width: 1,
        },
      },
    });
    layer.add(graphic);
  };

  const loadPolyline = async (record) => {
    const { id, stationList = [] } = record;
    let layer = map.findLayerById(id);
    if (!layer) {
      layer = new GraphicsLayer();
      layer.id = record.id;
      map.layers.add(layer);
    } else {
      delete record['ObjectId'];
      if (layer.title === md5(JSON.stringify(record))) return;
    }
    layer.removeAll();
    layer.title = md5(JSON.stringify(record));
    stationList.map((station) => {
      let prevPoint = [];
      station.map((lines) => {
        lines.map((item) => {
          const { line, status } = item;
          const paths = line.split(';').map((point) => {
            if (view3D) {
              return [...point.split(','), 0];
            } else {
              return point.split(',');
            }
          });
          const polyline = {
            type: 'polyline',
            paths,
          };
          const [firstPoint, ...restPoints] = paths;
          if (
            prevPoint?.length &&
            (firstPoint[0] !== prevPoint[0] || firstPoint[1] !== prevPoint[1])
          ) {
            const outGraphic = new Graphic({
              geometry: {
                type: 'polyline',
                paths: [prevPoint, firstPoint],
              },
              symbol: {
                type: 'simple-line',
                color: ROAD_STATUS[status].outlineColor,
                style: 'solid',
                width: '5px',
                join: 'round',
                cap: 'square',
              },
            });
            layer.add(outGraphic);
          }
          prevPoint = paths[paths.length - 1];
          const outGraphic = new Graphic({
            geometry: polyline,
            symbol: {
              type: 'simple-line',
              color: ROAD_STATUS[status].outlineColor,
              style: 'solid',
              width: '5px',
              join: 'round',
              cap: 'square',
            },
          });
          layer.add(outGraphic);
        });
      });
    });
  };

  const loadUAVPolyline = async (record) => {
    const { id, lines = [] } = record;
    let layer = map.findLayerById(id);
    if (!layer) {
      layer = new GraphicsLayer();
      layer.id = record.id;
      map.layers.add(layer);
    } else {
      delete record['ObjectId'];
      if (layer.title === md5(JSON.stringify(record))) return;
    }
    layer.removeAll();
    layer.title = md5(JSON.stringify(record));
    const paths = lines.map((item) => {
      if (view3D) {
        return [item.longitude, item.latitude, 0];
      }
      return [item.longitude, item.latitude];
    });
    if (paths.length) {
      if (paths.length > 1) {
        const outGraphic = new Graphic({
          geometry: {
            type: 'polyline',
            paths,
          },
          symbol: {
            type: 'simple-line',
            color: '#FF2F2F',
            style: 'solid',
            width: '4px',
            join: 'round',
            cap: 'square',
          },
        });
        layer.add(outGraphic);
      }
      const [longitude, latitude] = paths[paths.length - 1];
      const graphic = new Graphic({
        geometry: {
          type: 'point',
          longitude,
          latitude,
          spatialReference: view.spatialReference,
        },
        symbol: {
          type: 'picture-marker',
          url: uavIcon,
          height: 28,
          width: 28,
        },
      });
      layer.add(graphic);
    }
  };

  const loadGridList = async (record) => {
    const { id, gridList = [] } = record;
    let layer = map.findLayerById(id);
    if (!layer) {
      layer = new GraphicsLayer();
      layer.id = record.id;
      map.layers.add(layer);
    } else {
      delete record['ObjectId'];
      if (layer.title === md5(JSON.stringify(record))) return;
    }
    layer.removeAll();
    layer.title = md5(JSON.stringify(record));
    gridList.map((item) => {
      const polygon = new Polygon();
      const { route, lineWidth, lineSegmentType, lineSegmentColor, fillColor, fontColor, name,haloColor } =
        item;
      const points = route.split(';');
      const rings = points.map((point) => point.split(','));
      polygon.addRing(rings);
      const center = polygon.extent.center;
      let str = name;
      if (name && name.length > 10) {
        str = `${name.substring(0, 10)}...`;
      }
      const textSymbol = new TextSymbol(str);
      textSymbol.horizontalAlignment = 'center';
      textSymbol.color = new Color(fontColor);
      const font = new Font();
      font.size = '12pt';
      textSymbol.font = font;
      textSymbol.haloColor= haloColor
      textSymbol.haloSize= 1
      const symbol = new SimpleFillSymbol({
        color: new Color(fillColor),
        style: SimpleFillSymbol.STYLE_SOLID,
        outline: new SimpleLineSymbol({
          style: lineSegmentType,
          color: new Color(lineSegmentColor),
          width: lineWidth,
        }),
      });
      layer.add(new Graphic(polygon, symbol, { ignoreClick: true }));
      layer.add(new Graphic(center, textSymbol, { ignoreClick: true }));
    });
  };

  /**
   * 加载网格
   * @param record
   */
  const loadGrid = async (record) => {
    const id = record.id;
    let layer = map.findLayerById(id);
    if (!layer) {
      layer = new FeatureLayer({
        id: record.id,
        title: md5(JSON.stringify(record)),
        source: [],
        fields: [
          { name: 'id', alias: 'id', type: 'string' },
          { name: 'name', alias: 'name', type: 'string' },
          { name: 'type', alias: 'type', type: 'string' },
        ],
        objectIdField: 'ObjectId',
        outFields: ['*'],
        geometryType: 'polygon',
        spatialReference: view.spatialReference,
      });
      map.add(layer);
    } else {
      delete record['ObjectId'];
      if (layer.title === md5(JSON.stringify(record))) {
        return;
      }
    }
    console.log('cbdlcdcdc', record.haloColor);
    layer.title = md5(JSON.stringify(record));
    layer.labelingInfo = new LabelClass({
      symbol: {
        type: view3D ? 'label-3d' : 'text',
        symbolLayers: [
          {
            type: 'text',
            material: {
              color: record.textColor || MAP_GRID_OPTION.DEFAULT_FONT_COLOR,
            },
            size: 10,
          },
        ],
        color: record.textColor || MAP_GRID_OPTION.DEFAULT_FONT_COLOR,
        font: {
          size: 10,
        },
        haloColor: record.haloColor,
        haloSize: 1,
      },
      labelPlacement: 'above-center',
      labelExpressionInfo: {
        expression: 'DefaultValue($feature.name, "")',
      },
    });
    layer.renderer = {
      type: 'simple',
      symbol: {
        type: 'simple-fill',
        color: record.fillColor || MAP_GRID_OPTION.DEFAULT_FILL_COLOR,
        outline: {
          color: record.lineColor || MAP_GRID_OPTION.DEFAULT_LINE_COLOR,
          width: record.lineWidth || MAP_GRID_OPTION.DEFAULT_LINE_WIDTH,
          style: record.lineType || MAP_GRID_OPTION.DEFAULT_LINE_TYPE,
        },
      },
    };
    let polygon = {
      type: 'polygon', // autocasts as Polygon
      rings: record.routes,
    };
    let graphic = new Graphic({
      geometry: polygon,
      attributes: record,
    });
    const graphics = [graphic];
    const features = await layer.queryFeatures();
    await layer.applyEdits({ addFeatures: graphics });
    await layer.applyEdits({ deleteFeatures: features.features });
  };

  const loadMapImageLayer = (record) => {
    const { id, url } = record;
    let layer = map.findLayerById(id);
    if (layer) {
      delete record['ObjectId'];
      if (layer.title === md5(JSON.stringify(record))) return;
      map.remove(layer);
    }
    layer = new MapImageLayer({
      id,
      url,
      title: md5(JSON.stringify(record)),
    });
    map.add(layer, 4);
  };

  const setZIndex = () => {
    if (props.setZIndex) {
      props.setZIndex();
    }
  };

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%', minWidth: 545 }}>
      {/* 地图区域 */}
      <div
        id={id}
        ref={mapDiv}
        style={{
          width: '100%',
          height: height,
        }}
        className={showDrawing ? stylesIdx.drawing : ''}
      />
      {/* 工具栏 */}
      {map && view && !hideToolbar && (
        <Toolbar
          initMapData={initMapData}
          map={map}
          view={view}
          // changeView={changeView}
          sceneView={view3D}
          body={mapDiv}
          zIndex={zIndex}
          setZIndex={setZIndex}
          // FAM_LAYER_3D={FAM_LAYER_3D}
          closeMapDraw={closeMapDraw}
          showImage={showImage}
          changeShowImage={setShowImage}
          handleDraw={handleDraw}
        />
      )}
    </div>
  );
}

export default connect(({ arcgis, util }) => ({
  arcgis,
  util,
}))(ArcgisMap);

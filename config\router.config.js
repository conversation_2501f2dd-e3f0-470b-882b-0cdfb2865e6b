export default [
  {
    path: '/',
    redirect: '/user/login/',
  },

  {
    path: '/redirect',
    component: './redirect',
  },
  {
    path: '/led/screenQz/side',
    name: 'led大屏-大屏两侧',
    component: './led/screenQZ/side',
  },
  {
    path: '/led/screen',
    name: 'led大屏',
    component: './led/screen',
  },
  {
    path: '/led/screenQZ',
    name: 'led大屏-前沿智慧部',
    component: './led/screenQZ',
  },
  {
    path: '/led/screenSetting/:type',
    name: '大屏配置',
    component: './led/screenSetting',
  },
  {
    path: '/led/screenCC/:type',
    name: '厦门指挥中心大屏',
    component: './led/screenCC',
  },
  {
    path: '/dispatchInfoScreen',
    name: '出动信息屏',
    component: './dispatchInfoScreen',
  },

  {
    path: '/h5',
    name: '/h5',
    routes: [
      {
        path: '/h5/alarmSmsConfirm',
        name: 'confirmAddress',
        component: './h5/alarmSmsConfirm',
      },
      {
        component: './h5/404',
      },
    ],
  },
  {
    path: '/404',
    component: '404',
  },
  {
    path: '/transfer',
    component: 'transfer',
  },
  {
    path: '/home',
    name: 'home',
    component: './home',
  },
  {
    path: '/map',
    name: 'map',
    component: './map',
  },
  {
    path: '/alarm',
    component: '../layouts/AlarmLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        name: 'policeState',
        path: '/alarm/policeState/list',
        component: './alarm/policeState/index',
      },
      {
        name: 'rescue',
        path: '/alarm/rescue/list',
        component: './alarm/rescue',
      },
    ],
  },
  {
    path: '/backStage',
    component: '../layouts/UserCenterLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        name: 'userCenter',
        path: '/backStage/userCenter',
        component: './backStage/userCenter',
        authority: ['RM_SEE', 'UM_SEE', 'DM_SEE', 'DRM_SEE', 'WM_SEE', 'OUC_SEE'],
        routes: [
          {
            path: '/backStage/userCenter/userManage',
            name: 'userManage',
            authority: ['RM_SEE', 'UM_SEE', 'DM_SEE', 'DRM_SEE'],
            component: './backStage/userCenter/userManage',
          },
          {
            path: '/backStage/userCenter/wechatManage',
            name: 'wechatManage',
            authority: ['WM_SEE'],
            component: './backStage/userCenter/wechatManage',
          },
          {
            path: '/backStage/userCenter/outsideUserConfig',
            name: 'outsideUserConfig',
            authority: ['OUC_SEE'],
            component: './backStage/userCenter/outsideUserConfig',
          },
        ],
      },
      {
        name: 'appManage',
        path: '/backStage/appManage',
        authority: ['AMA_SEE', 'AMP_SEE', 'AMO_SEE'],
        component: './backStage/application',
      },
      {
        name: 'system',
        path: '/backStage/system',
        authority: [
          'IP_WHITE_LIST_SEE',
          'IP_INTER_LIST_SEE',
          'GRAYSCALE_LIST_SEE',
          'BLACK_LIST_SEE',
          'WHITE_LIST_SEE',
          'SEASONAL_CONFIG_SEE',
          'GLOBAL_MESSAGE_CONFIG_SEE',
          'USER_MESSAGE_CONFIG_SEE',
          'ALARM_DISPATCH_MESSAGE_CONFIG_SEE',
          'IMPORTANT_ALARM_NOTIFY_CONFIG_SEE',
          'POST_DUTY_PUSH_CONFIG_SEE',
          'APP_BANNER_CONFIG_SEE',
          'PAGE_NAME_CONFIG_SEE',
          'AUTHORIZATION_DETAIL_SEE',
          'PAGE_LABEL_CONFIG_SEE',
        ],
        component: './backStage/system',
        routes: [
          {
            name: 'nameList',
            path: '/backStage/system/nameList',
            authority: [
              'IP_WHITE_LIST_SEE',
              'IP_INTER_LIST_SEE',
              'GRAYSCALE_LIST_SEE',
              'BLACK_LIST_SEE',
              'WHITE_LIST_SEE',
            ],
            component: './backStage/system/nameList',
          },
          {
            path: '/backStage/system/seasonal',
            authority: ['SEASONAL_CONFIG_SEE'],
            name: 'seasonal',
            component: './backStage/system/seasonal',
          },
          {
            path: '/backStage/system/message',
            name: 'message',
            authority: [
              'GLOBAL_MESSAGE_CONFIG_SEE',
              'USER_MESSAGE_CONFIG_SEE',
              'ALARM_DISPATCH_MESSAGE_CONFIG_SEE',
              'IMPORTANT_ALARM_NOTIFY_CONFIG_SEE',
              'POST_DUTY_PUSH_CONFIG_SEE',
            ],
            component: './backStage/system/message',
            routes: [
              {
                path: '/backStage/system/message/global',
                authority: ['GLOBAL_MESSAGE_CONFIG_SEE'],
                name: 'global',
                component: './backStage/system/message/global',
              },
              {
                path: '/backStage/system/message/onwer',
                authority: ['USER_MESSAGE_CONFIG_SEE'],
                name: 'onwer',
                component: './backStage/system/message/onwer',
              },
              {
                path: '/backStage/system/message/dispatchMsg',
                authority: ['ALARM_DISPATCH_MESSAGE_CONFIG_SEE'],
                name: 'dispatchMsg',
                component: './backStage/system/message/dispatchMsg',
              },
              {
                title: '岗位职责推送配置',
                path: '/backStage/system/message/jobResponsibility',
                authority: ['POST_DUTY_PUSH_CONFIG_SEE'],
                name: 'jobResponsibility',
                component: './backStage/system/message/jobResponsibility',
              },
              {
                title: '重要警情通知配置',
                path: '/backStage/system/message/importantAlarm',
                authority: ['IMPORTANT_ALARM_NOTIFY_CONFIG_SEE'],
                name: 'importantAlarm',
                component: './backStage/system/message/importantAlarm',
              },
            ],
          },
          {
            path: '/backStage/system/appBanner',
            name: 'appBanner',
            authority: 'APP_BANNER_CONFIG_SEE',
            component: './backStage/system/appBanner',
          },
          {
            path: '/backStage/system/pageNameManage',
            name: 'pageNameManage',
            authority: ['PAGE_NAME_CONFIG_SEE', 'PAGE_LABEL_CONFIG_SEE'],
            component: './backStage/system/pageNameManage',
            routes: [
              {
                path: '/backStage/system/pageNameManage/page',
                authority: ['PAGE_NAME_CONFIG_SEE'],
                name: 'page',
                component: './backStage/system/pageNameManage/page',
              },
              {
                path: '/backStage/system/pageNameManage/label',
                authority: ['PAGE_LABEL_CONFIG_SEE'],
                name: 'label',
                component: './backStage/system/pageNameManage/label',
              },
            ],
          },
          {
            path: '/backStage/system/authInformation',
            authority: ['AUTHORIZATION_DETAIL_SEE'],
            name: 'authInformation',
            component: './backStage/system/authInformation',
          },
        ],
      },
      {
        path: '/backStage/dictionary',
        name: 'dictionary',
        authority: [
          'DRE_SEE',
          'LED_SMS_TEMPLATE_SEE',
          'PRE_DISPATCH_CONFIG_SEE',
          'ALARM_LINKAGE_CONTROLLER_SEE',
          'ALARM_TTS_BROADCAST_SEE',
          'INTELLIGENT_DISPATCHING_SEE',
          'BRIGADE_DISPATCHING_TYPE0_SEE',
          'DISPATCHING_MAIN_CAR_SEE',
          'ADDRESS_CLEAR_CITY_CONFIG_SEE',
          'AUTO_SEND_CONFIRM_SMS_SEE',
          'APP_ALARM_DRAFT_SEE',
          'SHOW_ALARM_CALL_LOG_SEE',
        ],
        component: './backStage/dictionary',
        routes: [
          {
            path: '/backStage/dictionary/dutyRole',
            name: 'dutyRole',
            authority: ['DRE_SEE'],
            component: './backStage/dictionary/dutyRole',
            hideInMenu: true,
            meta: {
              title: '值班角色',
              icon: 'dutyRole',
              activeIcon: 'dutyRoleActive',
            },
          },
          {
            path: '/backStage/dictionary/fttSMS',
            name: 'fttSMS',
            authority: ['LED_SMS_TEMPLATE_SEE'],
            component: './backStage/dictionary/fttSMS',
            hideInMenu: true,
            meta: {
              title: '街镇专职队LED短信模板',
              icon: 'fttSMS',
              activeIcon: 'fttSMSActive',
            },
          },
          {
            path: '/backStage/dictionary/PreStart',
            name: 'PreStart',
            authority: [
              'PRE_DISPATCH_CONFIG_SEE',
              'ALARM_LINKAGE_CONTROLLER_SEE',
              'ALARM_TTS_BROADCAST_SEE',
              'INTELLIGENT_DISPATCHING_SEE',
              'BRIGADE_DISPATCHING_TYPE0_SEE',
              'DISPATCHING_MAIN_CAR_SEE',
              'ADDRESS_CLEAR_CITY_CONFIG_SEE',
              'AUTO_SEND_CONFIRM_SMS_SEE',
              'APP_ALARM_DRAFT_SEE',
              'SHOW_ALARM_CALL_LOG_SEE',
            ],
            component: './backStage/dictionary/preStart',
            hideInMenu: true,
            meta: {
              title: '功能启停',
              icon: 'PreStart',
              activeIcon: 'preStartActive',
            },
          },
        ],
      },
      {
        name: 'version',
        path: '/backStage/version',
        authority: 'VERSION_CONFIG_SEE',
        component: './backStage/version',
      },
      {
        name: 'log',
        path: '/backStage/log',
        authority: ['LM_SEE'],
        component: './backStage/log',
      },
    ],
  },
  {
    path: '/application',
    component: '../layouts/ResourceLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        name: 'resource',
        path: '/application/resource',
        component: './application/resource',
        title: '基础资源管理',
        authority: [
          'BDH_SEE',
          'BDB_SEE',
          'BDU_SEE',
          'BDL_SEE',
          'BDBU_SEE',
          'FOLK_TEAM_SEE',
          'BDSU_SEE',
          'BDLU_SEE',
          'CM_SEE',
          'TYPE_PLAN_SEE',
          'PAD_SEE',
          'EQT_SEE',
          'BDRE_SEE',
          'REMOTE_IOT_MONITORING_SEE',
          'OUTDOOR_FIRE_HYDRANT_SEE',
          'OUTDOOR_FIRE_HYDRANT_REPAIR',
          'FULL_TIME_TEAM_SEE',
          // 'TROUBLE_CHECK_QUERY',
          'CAMERA_INFO_SEE',
          'DATA_UNIT_DRILL_SEE',
          'FIRE_WATER_SEE',
          'USER_DEVICE_SEE',
          'DATA_VIDEO_MANAGE_SEE',
        ],
        routes: [
          {
            path: '/application/resource/fireHydrant',
            name: 'fireHydrant',
            component: './application/resource/baseMapDada/fireHydrant',
            authority: 'BDH_SEE',
            title: '消火栓',
          },
          {
            path: '/application/resource/fireHydrantOut',
            name: 'fireHydrantOut',
            component: './application/resource/baseMapDada/fireHydrantOut',
            authority: ['OUTDOOR_FIRE_HYDRANT_SEE', 'OUTDOOR_FIRE_HYDRANT_REPAIR'],
            title: '室外消火栓',
          },
          {
            path: '/application/resource/miniFireStation',
            name: 'miniFireStation',
            component: './application/resource/baseMapDada',
            authority: 'BDB_SEE',
            title: '微型消防站',
          },
          {
            path: '/application/resource/headwaters',
            name: 'headwaters',
            component: './application/resource/baseMapDada',
            authority: 'FIRE_WATER_SEE',
            title: '水源管理',
          },
          {
            path: '/application/resource/keyUnit',
            name: 'keyUnit',
            component: './application/resource/baseMapDada',
            authority: 'BDU_SEE',
            title: '预案文件',
          },
          {
            path: '/application/resource/unitDrill',
            name: 'unitDrill',
            component: './application/resource/baseMapDada/unitDrill',
            authority: 'DATA_UNIT_DRILL_SEE',
            title: '单位演练',
          },
          {
            path: '/application/resource/keyPart',
            name: 'keyPart',
            component: './application/resource/baseMapDada',
            authority: 'BDL_SEE',
            title: '重点部位',
          },
          {
            path: '/application/resource/buildInfo',
            name: 'buildInfo',
            component: './application/resource/baseMapDada',
            authority: 'BDBU_SEE',
            title: '建筑物信息',
          },
          {
            path: '/application/resource/jointUnit',
            name: 'jointUnit',
            component: './application/resource/baseMapDada',
            authority: 'BDSU_SEE',
            title: '联勤保障单位',
          },
          {
            path: '/application/resource/emergencyUnit',
            name: 'emergencyUnit',
            component: './application/resource/baseMapDada',
            authority: 'BDLU_SEE',
            title: '应急联动单位',
          },
          {
            path: '/application/resource/folkTeam',
            name: 'folkTeam',
            authority: 'FOLK_TEAM_SEE',
            component: './application/resource/baseMapDada',
            title: '民间队',
          },
          {
            path: '/application/resource/professional',
            name: 'professional',
            authority: 'FULL_TIME_TEAM_SEE',
            component: './application/resource/baseMapDada',
            title: '街镇专职队',
          },
          {
            path: '/application/resource/carManage',
            name: 'carManage',
            authority: 'CM_SEE',
            component: './application/resource/carManage',
            title: '车辆管理',
          },
          {
            path: '/application/resource/carTerminal',
            name: 'carTerminal',
            authority: 'PAD_SEE',
            component: './application/resource/carTerminal',
            title: '车载终端',
          },
          {
            path: '/application/resource/equipment',
            authority: 'EQT_SEE',
            name: 'equipment',
            component: './application/resource/equipment',
            title: '装备器材',
          },
          {
            path: '/application/resource/fireRescue',
            authority: 'BDRE_SEE',
            name: 'fireRescue',
            component: './application/resource/fireRescue',
            title: '灭火救援专家',
          },
          {
            path: '/application/resource/monitor',
            name: 'monitor',
            component: './application/resource/baseMapDada',
            authority: 'REMOTE_IOT_MONITORING_SEE',
            title: '远程物联网监控',
          },
          // {
          //   path: '/application/resource/hidePerils',
          //   name: 'hidePerils',
          //   component: './application/resource/baseMapDada',
          //   authority: 'TROUBLE_CHECK_QUERY',
          //   title: '隐患排查',
          // },
          {
            path: '/application/resource/userDevice',
            name: 'userDevice',
            component: './application/resource/baseMapDada',
            authority: 'USER_DEVICE_SEE',
            title: '人员装备',
          },
          {
            path: '/application/resource/cameraLabel',
            name: 'cameraLabel',
            component: './application/resource/cameraLabel',
            authority: 'CAMERA_INFO_SEE',
            title: '雪亮工程',
          },
          {
            path: '/application/resource/videoManage',
            name: 'videoManage',
            component: './application/resource/videoManage',
            authority: 'DATA_VIDEO_MANAGE_SEE',
            title: '视频管理',
          },
        ],
      },
      {
        name: 'process',
        path: '/application/process',
        component: './application/process',
        authority: ['FLOW_ROLE_CONFIG_SEE', 'FLOW_CHART_SEE', 'CUSTOMER_FORM_SEE'],
        routes: [
          {
            path: '/application/process/flowRole',
            name: 'flowRole',
            authority: ['FLOW_ROLE_CONFIG_SEE'],
            component: './application/process/flowRole',
            title: '流程角色配置',
          },
          {
            path: '/application/process/workflow',
            name: 'workflow',
            authority: ['FLOW_CHART_SEE', 'CUSTOMER_FORM_SEE'],
            component: './application/process/workflow',
            title: '流程管理',
          },
        ],
      },
      {
        name: 'dispatch',
        path: '/application/dispatch',
        authority: [
          'PT_SEE',
          'SBF_SEE',
          'MVB_SEE',
          'PCF_SEE',
          'PTM_SEE',
          'CT_SEE',
          'DUT_SEE',
          'DPT_SEE',
        ],
        component: './application/dispatch',
        routes: [
          {
            path: '/application/dispatch/typePlan',
            name: 'typePlan',
            authority: ['PT_SEE'],
            component: './application/dispatch/typePlan',
            title: '类型调派方案',
          },
          {
            path: '/application/dispatch/fireStation',
            name: 'fireStation',
            authority: 'SBF_SEE',
            component: './application/dispatch/fireStation',
          },
          {
            path: '/application/dispatch/carBindUser',
            name: 'carBindUser',
            authority: 'MVB_SEE',
            component: './application/dispatch/carBindUser',
          },
          {
            path: '/application/dispatch/proTeam',
            name: 'proTeam',
            authority: ['PCF_SEE', 'PTM_SEE'],
            component: './application/dispatch/proTeam',
          },
          {
            path: '/application/dispatch/carPlan',
            name: 'carPlan',
            authority: ['CT_SEE'],
            component: './application/dispatch/carPlan',
          },
          {
            path: '/application/dispatch/keyUnitPlan',
            name: 'keyUnitPlan',
            authority: ['DUT_SEE'],
            component: './application/dispatch/unitPlan',
          },
          {
            path: '/application/dispatch/professionalPlan',
            name: 'professionalPlan',
            authority: ['DPT_SEE'],
            component: './application/dispatch/professionalPlan',
          },
        ],
      },
      {
        name: 'match',
        path: '/application/match',
        authority: [
          'HWB_SEE',
          'NTS_SEE',
          'RGS_SEE',
          'CM_LINKAGE_CONFIG_SEE',
          'TASK_LIST_SEE',
          'ALRM_TYPE_MATCH_RULE_SEE',
          'UAV_WHITE_LIST_SEE',
          'ALARM_TYPE_TOP_SEE',
          'DATA_FULL_ELEMENT_SEE',
          'QUERY_GUIDE_SEE',
          'APP_RING_CONFIG_SEE',
          'TRI_COLOUR_LIGHT_CONFIG_SEE',
        ],
        component: './application/match',
        routes: [
          {
            path: '/application/match/alarmTypeRule',
            name: 'alarmTypeRule',
            authority: ['ALRM_TYPE_MATCH_RULE_SEE'],
            component: './application/match/alarmTypeRule',
            title: '警情类型匹配规则',
          },
          {
            path: '/application/match/hotword',
            name: 'hotword',
            authority: ['HWB_SEE'],
            component: './application/match/hotword',
            title: '热词库',
          },
          {
            path: '/application/match/monitor',
            name: 'monitor',
            authority: ['NTS_SEE'],
            component: './application/match/monitor',
            title: '规范用语',
          },
          {
            path: '/application/match/helpguid',
            name: 'helpguid',
            authority: ['RGS_SEE'],
            component: './application/match/helpguid',
            title: '救援指引',
          },
          {
            path: '/application/match/linkage',
            name: 'linkage',
            component: './application/match/linkage',
            authority: ['CM_LINKAGE_CONFIG_SEE'],
          },
          // {
          //   path: '/application/match/taskList',
          //   name: 'taskList',
          //   component: './application/match/taskList',
          //   authority: ['TASK_LIST_SEE'],
          // },
          {
            path: '/application/match/uavWhiteList',
            name: 'uavWhiteList',
            component: './application/match/uavWhiteList',
            authority: ['UAV_WHITE_LIST_SEE'],
          },
          {
            path: '/application/match/typeTop',
            name: 'typeTop',
            component: './application/match/typeTop',
            authority: ['ALARM_TYPE_TOP_SEE'],
          },
          {
            path: '/application/match/safety',
            name: 'safety',
            component: './application/match/safety',
            authority: ['DATA_FULL_ELEMENT_SEE'],
          },
          {
            path: '/application/match/enquiryManage',
            name: 'enquiryManage',
            component: './application/match/enquiryManage',
            authority: ['QUERY_GUIDE_SEE'],
          },
          {
            path: '/application/match/appRing',
            name: 'appRing',
            component: './application/match/appRing',
            authority: ['APP_RING_CONFIG_SEE'],
          },
          {
            path: '/application/match/alarmLigh',
            name: 'alarmLigh',
            authority: ['TRI_COLOUR_LIGHT_CONFIG_SEE'],
            component: './application/alarmLigh',
            title: '三色灯配置',
          },
          {
            path: '/application/match/taskManagementTemplate',
            name: 'taskManagementTemplate',
            authority: ['TASK_LIST_SEE'],
            component: './application/match/taskManagementTemplate',
            title: '任务模版',
          },
        ],
      },
      {
        name: 'dutyManage',
        path: '/application/dutyManage',
        component: './application/dutyManage',
        authority: ['POR_SEE', 'FSN_SEE', 'DDY_SEE', 'BDY_SEE', 'CSS_SEE'],
        routes: [
          {
            path: '/application/dutyManage/order',
            name: 'order',
            authority: ['POR_SEE', 'FSN_SEE', 'DDY_SEE', 'BDY_SEE'],
            component: './application/dutyManage/order',
          },
          {
            path: '/application/dutyManage/record',
            authority: ['CSS_SEE'],
            name: 'record',
            component: './application/dutyManage/record',
          },
        ],
      },
    ],
  },
  //值班管理
  {
    path: '/onDutyManage',
    component: '../layouts/OnDutyLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        name: 'dutyManage',
        path: '/onDutyManage/dutyManage',
        component: './onDutyManage/dutyManage',
        authority: [
          'ML_DUTY_MANAGE_INFO_SEE',
          'ML_DUTY_MANAGE_POST_SEE',
          'ML_DUTY_MANAGE_HOLIDAY_SEE',
          'ML_DUTY_MANAGE_LOG_SEE',
        ],
        routes: [
          {
            path: '/onDutyManage/dutyManage/dutyInfo',
            name: 'dutyInfo',
            authority: ['ML_DUTY_MANAGE_INFO_SEE'],
            component: './onDutyManage/dutyManage/dutyInfo',
          },
          {
            path: '/onDutyManage/dutyManage/dutyJob',
            authority: ['ML_DUTY_MANAGE_POST_SEE'],
            name: 'dutyJob',
            component: './onDutyManage/dutyManage/dutyJob',
          },
          {
            name: 'holidayManager',
            authority: ['ML_DUTY_MANAGE_HOLIDAY_SEE'],
            path: '/onDutyManage/dutyManage/holidayManager',
            component: './onDutyManage/dutyManage/holidayManager',
            title: '值班节假日信息',
          },
          {
            name: 'dutyLogs',
            authority: ['ML_DUTY_MANAGE_LOG_SEE'],
            path: '/onDutyManage/dutyManage/dutyLogs',
            component: './onDutyManage/dutyManage/dutyLogs',
            title: '值班日志',
          },
        ],
      },
    ],
  },
  // 消防队伍人员结构图
  {
    name: 'teamStatistics',
    path: '/teamStatistics',
    Routes: ['src/pages/Authorized'],
    component: './team/statistics',
    authority: 'TEAM_AMAP_SEE',
    title: '队务一张图',
  },
  // // 隐患排查 统计大屏
  // {
  //   path: '/hidePerilsLargeScreen',
  //   name: 'hidePerilsLargeScreen',
  //   Routes: ['src/pages/Authorized'],
  //   component: './application/resource/baseMapDada/largeScreen',
  //   authority: 'TEAM_AMAP_SEE',
  //   title: '统计大屏',
  // },
  // 警情录入
  {
    path: '/alarmEntry',
    name: 'alarmEntry',
    Routes: ['src/pages/Authorized'],
    component: './alarm/additional',
    authority: 'ALARM_ENTER_ADD',
  },
  // 通知公告
  {
    name: 'notice',
    path: '/notice',
    Routes: ['src/pages/Authorized'],
    component: './notice',
    authority: 'NM_SEE',
  },
  // 干部管理
  {
    name: 'cadreManagement',
    path: '/cadreManagement',
    Routes: ['src/pages/Authorized'],
    component: './cadreManagement',
    authority: 'LEADER_MANAGE_SEE',
  },
  // 短信查询
  {
    name: 'sms',
    path: '/sms',
    Routes: ['src/pages/Authorized'],
    component: './sms',
    authority: 'SMS_SEARCH_SEE',
  },
  // 短信通知
  {
    name: 'smsNotice',
    path: '/smsNotice',
    Routes: ['src/pages/Authorized'],
    component: './smsNotice',
    authority: 'SMS_NOTICE_SEE',
  },
  // 任务管理
  {
    name: 'taskManage',
    path: '/taskManage',
    Routes: ['src/pages/Authorized'],
    component: './fightingMap/components/client/planManage',
    authority: 'TASK_MANAGE_SEE',
  },
  // // 防火监督模块
  // {
  //   name: 'fireSupervisionManage',
  //   path: '/fireSupervisionManage',
  //   Routes: ['src/pages/Authorized'],
  //   component: './fireSupervision/manage',
  //   authority: ['FRS_TRAIN_SEE','FRS_NOTICE_SEE'],
  // },
  {
    name: 'contactLetter',
    path: '/contactLetter',
    Routes: ['src/pages/Authorized'],
    component: './contactLetter',
    authority: ['M_CONTACT_LETTER_MY_SEE', 'M_CONTACT_LETTER_RECEIVE_SEE'],
  },
  {
    name: 'customFlow',
    path: '/customFlow',
    Routes: ['src/pages/Authorized'],
    component: './customFlow',
    authority: ['M_CONTACT_LETTER_MY_SEE', 'M_CONTACT_LETTER_RECEIVE_SEE'],
  },
  // 请假日历
  {
    name: 'leaveCalendar',
    path: '/leaveCalendar',
    Routes: ['src/pages/Authorized'],
    component: './leaveCalendar',
    authority: 'LEAVE_CALENDAR_SEE',
  },
  // 防火监督-接警统计大屏
  {
    name: 'alarmStatistics',
    path: '/alarmStatistics',
    Routes: ['src/pages/Authorized'],
    component: './fireSupervision/alarmStatistics',
    authority: 'ALARM_RECEIVE_AMAP_SEE',
    title: '接处警统计大屏',
  },
  // 采购管理
  {
    name: 'purchaseManagement',
    path: '/finance/buyManage',
    Routes: ['src/pages/Authorized'],
    component: './purchaseManagement',
    authority: 'SBM_SEE',
  },
  // 访客管理
  {
    name: 'visitor',
    path: '/visitor',
    Routes: ['src/pages/Authorized'],
    authority: 'Visitor_Appointment_SEE',
    component: './visitor',
  },
  // 审计监督管理
  {
    path: '/finance',
    name: 'finance',
    component: '../layouts/CustomizeLayer',
    Routes: ['src/pages/Authorized'],
    authority: ['SFAE_SEE', 'SFAP_SEE'],
    routes: [
      {
        path: '/finance/report',
        name: 'report',
        authority: 'SFAE_SEE',
        component: './finance/report',
      },
      {
        path: '/finance/procurement',
        name: 'procurement',
        authority: 'SFAP_SEE',
        component: './finance/procurement',
      },
    ],
  },
  {
    path: '/knowledge',
    name: 'knowledge',
    component: '../layouts/CustomizeLayer',
    Routes: ['src/pages/Authorized'],
    authority: [
      'LAW_CONFIG_SEE',
      'TYPE_PLAN_SEE',
      'DEVICE_KNOW_SEE',
      'CHEMICALS_KNOWLEDGE_NEW',
      'CHEMICALS_KNOWLEDGE_SEE',
      'EDU_KNOWLEDGE_BASE_SEE',
    ],
    routes: [
      {
        path: '/knowledge/urgency',
        name: 'urgency',
        authority: ['TYPE_PLAN_SEE'],
        component: './knowledge/urgency',
        routes: [
          {
            path: '/knowledge/urgency/typePlan',
            name: 'typePlan',
            authority: ['TYPE_PLAN_SEE'],
            component: './knowledge/urgency/typePlan',
          },
        ],
      },
      {
        path: '/knowledge/law',
        name: 'law',
        authority: 'LAW_CONFIG_SEE',
        component: './knowledge/law',
      },
      {
        path: '/knowledge/chemicals',
        name: 'chemicals',
        authority: 'CHEMICALS_KNOWLEDGE_SEE',
        component: './knowledge/chemicals',
      },
      {
        path: '/knowledge/hazardous',
        name: 'hazardous',
        authority: 'CHEMICALS_KNOWLEDGE_NEW',
        component: './knowledge/hazardous',
      },
      {
        path: '/knowledge/device',
        name: 'know.device',
        authority: 'DEVICE_KNOW_SEE',
        component: './knowledge/device',
      },
      {
        path: '/knowledge/knowledgeBase',
        name: 'knowledgeBase',
        authority: 'EDU_KNOWLEDGE_BASE_SEE',
        component: './knowledge/knowledgeBase',
      },
    ],
  },
  {
    path: '/modal/detail',
    name: 'alarmmanage',
    component: './fightingMap/alarmComponents/AlarmDetail',
    hideInMenu: true,
  },
  {
    path: '/screen',
    component: '../layouts/LargeScreenLayout',
    authority: 'SO_SEE',
    routes: [
      {
        path: '/screen/overview/',
        name: 'overview',
        authority: 'SO_SEE',
        component: './screen/overview',
      },
      {
        path: '/screen/realtime/',
        name: 'realtime',
        authority: 'SO_SEE',
        component: './screen/realtime',
      },
    ],
  },
  {
    path: '/fighting',
    component: '../layouts/FightingMapLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['FM_SEE'],
    routes: [
      {
        path: '/fighting/fightingMap',
        name: 'fightingMap',
        component: './fightingMap',
      },
    ],
  },
  {
    path: '/intelCommand',
    component: '../layouts/IntelCommandMapLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['FM_SEE'],
    routes: [
      {
        path: '/intelCommand/intelCommandMap',
        name: 'intelCommand',
        component: './intelCommandMap',
      },
    ],
  },
  {
    path: '/alarmReviewDetail',
    component: '../layouts/FightingMapLayout',
    meta: {
      subTitle: '战评复盘',
      hideNotice: true,
    },
    Routes: ['src/pages/Authorized'],
    authority: ['ALARM_REPLAY_LIST_REPLAY'],
    routes: [
      {
        path: '/alarmReviewDetail/alarmReviewMap',
        name: 'alarmReviewMap',
        component: './alarmReviewMap',
        authority: 'ALARM_REPLAY_LIST_REPLAY',
      },
    ],
  },

  {
    path: '/track',
    component: '../layouts/TrackLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['FM_SEE'],
    routes: [
      {
        path: '/track/tracking',
        name: 'tracking',
        component: './tracking',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/disasterRelief',
    name: 'disasterRelief',
    component: './disasterRelief',
    authority: ['MAP_SAFE_DYNAMIC_SEE'],
    hideInMenu: true,
  },
  {
    name: 'opsManage',
    path: '/opsManage',
    component: '../layouts/DinnerLayout',
    authority: ['FIRE_APP_OPERATION_CONFIG_SEE', 'OM_BASE_UNIFIED_TIMING_SEE'],
    routes: [
      {
        path: '/opsManage/opsManagement',
        name: 'opsManagement',
        authority: 'FIRE_APP_OPERATION_CONFIG_SEE',
        routes: [
          {
            path: '/opsManage/opsManagement/appSettings',
            name: 'appSettings',
            component: './ops/appSettings',
            authority: 'FIRE_APP_OPERATION_CONFIG_SEE',
          },
        ],
      },
      {
        path: '/opsManage/baseSettings',
        name: 'baseSettings',
        authority: 'OM_BASE_UNIFIED_TIMING_SEE',
        routes: [
          {
            path: '/opsManage/baseSettings/time',
            name: 'time',
            component: './ops/baseSettings',
            authority: 'OM_BASE_UNIFIED_TIMING_SEE',
          },
        ],
      },
    ],
  },
  {
    path: '/team',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: [
      'M_TEAM_LABEL_SEE',
      'M_TEAM_DEEDS_SEE',
      'M_TEAM_MEMBER_MANAGEMENT_INFO_SEE',
      'M_TEAM_USERAUDIT_USERINFO_SEE',
      'M_TEAM_USERAUDIT_TYPECHANGE_SEE',
      'M_TEAM_MEMBER_MANAGEMENT_SALARY_SEE',
      'M_TEAM_LEAVE_APPLY_SEE',
      'M_TEAM_LEAVE_APPROVAL_SEE',
      'M_TEAM_LEAVE_RECORD_SEE',
      'M_TEAM_LEAVE_COUNT_SEE',
      'M_TEAM_SIGNIN_SEE',
      'F_TEAM_PHONE_APP_SEE',
      'F_TEAM_PHONE_MONITOR_SEE',
      'F_TEAM_PHONE_MANAGE_SEE',
      'F_TEAM_PHONE_RED_LIST_SEE',
    ],
    routes: [
      {
        name: 'fireUser',
        path: '/team/fireUser',
        authority: [
          'M_TEAM_LABEL_SEE',
          'M_TEAM_DEEDS_SEE',
          'M_TEAM_MEMBER_MANAGEMENT_INFO_SEE',
          'M_TEAM_USERAUDIT_USERINFO_SEE',
          'M_TEAM_USERAUDIT_TYPECHANGE_SEE',
          'M_TEAM_MEMBER_MANAGEMENT_SALARY_SEE',
        ],
        routes: [
          {
            path: '/team/fireUser/info',
            name: 'info',
            component: './team/fireUser',
            authority: 'M_TEAM_MEMBER_MANAGEMENT_INFO_SEE',
            title: '人员信息',
          },
          {
            path: '/team/fireUser/userAudit/userInfo',
            name: 'userInfo',
            component: './team/userAudit/userInfo',
            authority: 'M_TEAM_USERAUDIT_USERINFO_SEE',
            title: '人员信息审核',
          },
          {
            path: '/team/fireUser/userAudit/typeChange',
            name: 'typeChange',
            component: './team/userAudit/typeChange',
            authority: 'M_TEAM_USERAUDIT_TYPECHANGE_SEE',
            title: '类型变更审核',
          },
          {
            path: '/team/fireUser/label',
            name: 'label',
            component: './team/label',
            authority: ['M_TEAM_LABEL_SEE'],
            title: '标签管理',
          },
          {
            path: '/team/fireUser/deeds',
            name: 'deeds',
            component: './team/deeds',
            authority: ['M_TEAM_DEEDS_SEE'],
            title: '事迹管理',
          },
          {
            path: '/team/fireUser/salary',
            name: 'salary',
            component: './team/salary',
            authority: 'M_TEAM_MEMBER_MANAGEMENT_SALARY_SEE',
            title: '薪资管理',
          },
        ],
      },
      {
        // 请销假
        path: '/team/leave',
        name: 'leave',
        Routes: ['src/pages/Authorized'],
        authority: [
          'M_TEAM_LEAVE_APPLY_SEE',
          'M_TEAM_LEAVE_APPROVAL_SEE',
          'M_TEAM_LEAVE_RECORD_SEE',
          'M_TEAM_LEAVE_COUNT_SEE',
        ],
        routes: [
          {
            path: '/team/leave/application',
            name: 'application',
            component: './team/leave/application',
            authority: 'M_TEAM_LEAVE_APPLY_SEE',
            title: '请假申请',
          },
          {
            path: '/team/leave/approval',
            name: 'approval',
            component: './team/leave/approval',
            authority: 'M_TEAM_LEAVE_APPROVAL_SEE',
            title: '请假审批',
          },
          {
            path: '/team/leave/record',
            name: 'record',
            component: './team/leave/record',
            authority: 'M_TEAM_LEAVE_RECORD_SEE',
            title: '请假记录',
          },
          {
            path: '/team/leave/count',
            name: 'count',
            component: './team/leave/count',
            authority: 'M_TEAM_LEAVE_COUNT_SEE',
            title: '请假统计',
          },
        ],
      },
      {
        // 点名签到
        name: 'signin',
        path: '/team/signin',
        Routes: ['src/pages/Authorized'],
        component: './team/signin',
        authority: 'M_TEAM_SIGNIN_SEE',
      },
      // 手机管理
      {
        name: 'phone',
        path: '/team/phone',
        Routes: ['src/pages/Authorized'],
        authority: [
          'F_TEAM_PHONE_APP_SEE',
          'F_TEAM_PHONE_MONITOR_SEE',
          'F_TEAM_PHONE_MANAGE_SEE',
          'F_TEAM_PHONE_RED_LIST_SEE',
          'F_PHONE_POSITION_SEE',
        ],
        routes: [
          // 应用监测
          {
            path: '/team/phone/app',
            name: 'app',
            component: './team/phone/application',
            authority: 'F_TEAM_PHONE_APP_SEE',
            title: '应用监测',
          },
          // 手机监测
          {
            path: '/team/phone/monitor',
            name: 'monitor',
            component: './team/phone/monitor',
            authority: 'F_TEAM_PHONE_MONITOR_SEE',
            title: '敏感词监测',
          },
          // 敏感词管理
          {
            path: '/team/phone/word',
            name: 'word',
            component: './team/phone/word',
            authority: 'F_TEAM_PHONE_MANAGE_SEE',
            title: '敏感词管理',
          },
          // 手机红名单
          {
            path: '/team/phone/redlist',
            name: 'redlist',
            component: './team/phone/redlist',
            authority: 'F_TEAM_PHONE_RED_LIST_SEE',
            title: '手机红名单',
          },
          // 手机定位
          {
            path: '/team/phone/location',
            name: 'location',
            component: './team/phone/location',
            authority: 'F_PHONE_POSITION_SEE',
            title: '手机定位',
          },
        ],
      },
    ],
  },
  // 公务用车
  {
    path: '/carManagement',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: [
      'M_TEAM_CAR_GPS_SEE',
      'M_TEAM_CAR_LIST_SEE',
      'M_TEAM_CAR_NOTIFY_SEE',
      'M_TEAM_CAR_STATISTICS_SEE',
      'M_TEAM_FIRECAR_REFUEL_SEE',
      'M_TEAM_CAR_FENCE_SEE',
      'M_TEAM_KEY_SEE',
    ],
    routes: [
      // 公务用车
      {
        name: 'car',
        path: '/carManagement/car',
        authority: [
          'M_TEAM_CAR_GPS_SEE',
          'M_TEAM_CAR_LIST_SEE',
          'M_TEAM_CAR_NOTIFY_SEE',
          'M_TEAM_CAR_STATISTICS_SEE',
          'M_TEAM_FIRECAR_REFUEL_SEE',
          'M_TEAM_CAR_FENCE_SEE',
        ],
        routes: [
          {
            path: '/carManagement/car/location',
            name: 'location',
            component: './team/carManage/location',
            authority: 'M_TEAM_CAR_GPS_SEE',
            title: '车辆定位',
          },
          {
            path: '/carManagement/car/apply',
            name: 'apply',
            component: './team/carManage/apply',
            authority: 'M_TEAM_CAR_LIST_SEE',
            title: '申请记录',
          },
          {
            path: '/carManagement/car/statistics',
            name: 'statistics',
            component: './team/carManage/statistics',
            authority: 'M_TEAM_CAR_STATISTICS_SEE',
            title: '派车统计',
          },
          {
            path: '/carManagement/car/fireCar',
            name: 'fireCar',
            component: './team/carManage/fireCar',
            authority: 'M_TEAM_FIRECAR_REFUEL_SEE',
            title: '消防车辆',
          },
          {
            path: '/carManagement/car/fence',
            name: 'fence',
            component: './team/carManage/fence',
            authority: 'M_TEAM_CAR_FENCE_SEE',
            title: '地理围栏',
          },
        ],
      },
      {
        // 智能钥匙柜
        path: '/carManagement/cabinet',
        name: 'cabinet',
        component: './team/cabinet',
        authority: 'M_TEAM_KEY_SEE',
        title: '智能钥匙柜',
      },
    ],
  },
  // 考勤管理
  {
    path: '/attendance',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['M_TEAM_ATTENDANCE_DAILY_SEE', 'M_TEAM_ATTENDANCE_SET_SEE', 'M_TEAM_POINT_SET_SEE'],
    routes: [
      {
        path: '/attendance/daily',
        name: 'daily',
        component: './team/attendance/daily',
        authority: 'M_TEAM_ATTENDANCE_DAILY_SEE',
        title: '考勤记录',
      },
      {
        path: '/attendance/set',
        name: 'set',
        component: './team/attendance/set',
        authority: 'M_TEAM_ATTENDANCE_SET_SEE',
        title: '考勤设置',
      },
      {
        path: '/attendance/clockPoint',
        name: 'clockPoint',
        component: './team/attendance/clockPoint',
        authority: 'M_TEAM_POINT_SET_SEE',
        title: '打卡点设置',
      },
    ],
  },
  // 服装管理
  {
    path: '/clothingManagement',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['M_USER_CLOTHES_INFO_SEE', 'M_USER_CLOTHES_USER_SEE'],
    routes: [
      {
        path: '/clothingManagement/warehouse',
        name: 'clothingManagementWarehouse',
        authority: 'M_USER_CLOTHES_INFO_SEE',
        component: './clothingManagement/warehouse',
        title: '服装仓库',
      },
      {
        path: '/clothingManagement/detail',
        name: 'clothingManagementDetail',
        authority: 'M_USER_CLOTHES_USER_SEE',
        component: './clothingManagement/detail',
        title: '个人服装详情',
      },
    ],
  },
  // 宣传人员管理
  {
    path: '/publicityEducation',
    component: '../layouts/DinnerLayout',
    Routes: ['src/pages/Authorized'],
    authority: [
      'TRAIN_APPLY_SHUNT_SEE',
      'PUBLICITY_MANAGEMENT_USER_SEE',
      'PUBLICITY_MANAGEMENT_EQUIPMENT_SEE',
      'PUBLICITY_MANAGEMENT_DEPT_SEE',
      'UNIT_TRAIN_MANAGE_SEE',
      'TRAIN_STATISTICS_USER_SEE',
      'TRAIN_STATISTICS_MANAGE_SEE',
      'TRAIN_STATISTICS_EXAM_SEE',
    ],
    routes: [
      {
        name: 'publicityManagement',
        path: '/publicityEducation/publicityManagement',
        component: './publicityEducation/publicityManagement',
        authority: [
          'PUBLICITY_MANAGEMENT_USER_SEE',
          'PUBLICITY_MANAGEMENT_EQUIPMENT_SEE',
          'PUBLICITY_MANAGEMENT_DEPT_SEE',
        ],
        routes: [
          {
            path: '/publicityEducation/publicityManagement/publicist',
            name: 'publicist',
            authority: ['PUBLICITY_MANAGEMENT_USER_SEE', 'PUBLICITY_MANAGEMENT_DEPT_SEE'],
            component: './publicityEducation/publicityManagement/publicist',
            title: '宣传人员管理',
          },
          {
            path: '/publicityEducation/publicityManagement/equip',
            name: 'equip',
            authority: 'PUBLICITY_MANAGEMENT_EQUIPMENT_SEE',
            component: './publicityEducation/publicityManagement/equip',
            title: '宣传队伍装备管理',
          },
        ],
      },
      {
        path: '/publicityEducation/trainManage',
        name: 'trainManage',
        authority: [
          'TRAIN_APPLY_SHUNT_SEE',
          'UNIT_TRAIN_MANAGE_SEE',
          'TRAIN_STATISTICS_USER_SEE',
          'TRAIN_STATISTICS_MANAGE_SEE',
          'TRAIN_STATISTICS_EXAM_SEE',
        ],
        routes: [
          {
            path: '/publicityEducation/trainManage/trainApplyManage',
            name: 'trainApplyManage',
            authority: ['TRAIN_APPLY_SHUNT_SEE'],
            routes: [
              {
                path: '/publicityEducation/trainManage/trainApplyManage/shunt',
                name: 'shunt',
                component: './publicityEducation/trainManage/application/shunt',
                authority: 'TRAIN_APPLY_SHUNT_SEE',
              },
            ],
          },
          {
            path: '/publicityEducation/trainManage/propagandaTrainManage',
            name: 'propagandaTrainManage',
            authority: 'UNIT_TRAIN_MANAGE_SEE',
            component: './publicityEducation/trainManage/propagandaTrainManage',
            title: '培训管理',
          },
          {
            path: '/publicityEducation/trainManage/trainingStatisticsManage',
            name: 'trainingStatistics',
            component: './publicityEducation/trainManage/trainingStatisticsManage',
            authority: [
              'TRAIN_STATISTICS_USER_SEE',
              'TRAIN_STATISTICS_MANAGE_SEE',
              'TRAIN_STATISTICS_EXAM_SEE',
            ],
          },
        ],
      },
    ],
  },
  // 消防站管理
  {
    path: '/stationManage',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['OUT_REPORT_SEE'],
    routes: [
      {
        name: 'fireStationOutReport',
        path: '/stationManage/fireStation',
        component: './stationManage/fireStation',
        authority: ['OUT_REPORT_SEE'],
        routes: [
          {
            path: '/stationManage/fireStation/outReport',
            name: 'outReport',
            authority: ['OUT_REPORT_SEE'],
            component: './stationManage/fireStation/outReport',
            title: '外出报备',
          },
        ],
      },
    ],
  },
  // 干部考核
  {
    path: '/leaderExamine',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: [
      'M_LEADER_EXAMINE_TERM_SEE',
      'M_LEADER_EXAMINE_MANAGE_SEE',
      'M_LEADER_EXAMINE_ANALYSIS_SEE',
      'M_LEADER_EXAMINE_AUTH_SEE',
      'M_LEADER_EXAMINE_QUERY_SEE',
      'M_LEADER_EXAMINE_AUTH_EVAL_SEE',
      'M_LEADER_EXAMINE_LEVEL_EVAL_SEE',
    ],
    routes: [
      {
        path: '/leaderExamine/term',
        name: 'term',
        authority: 'M_LEADER_EXAMINE_TERM_SEE',
        component: './leaderExamine/term',
        title: '条款管理',
      },
      {
        path: '/leaderExamine/exam',
        name: 'exam',
        authority: 'M_LEADER_EXAMINE_MANAGE_SEE',
        component: './leaderExamine/exam',
        title: '考核管理',
      },
      {
        path: '/leaderExamine/analysis',
        name: 'analysis',
        authority: 'M_LEADER_EXAMINE_ANALYSIS_SEE',
        component: './leaderExamine/analysis',
        title: '梳理分析',
      },
      {
        path: '/leaderExamine/check',
        name: 'check',
        authority: 'M_LEADER_EXAMINE_AUTH_SEE',
        component: './leaderExamine/check',
        title: '领导审核',
      },
      {
        path: '/leaderExamine/personnel',
        name: 'personnel',
        authority: 'M_LEADER_EXAMINE_PERSONNEL_SEE',
        component: './leaderExamine/personnel',
        title: '人事处确认',
      },
      {
        path: '/leaderExamine/result',
        name: 'result',
        authority: 'M_LEADER_EXAMINE_QUERY_SEE',
        component: './leaderExamine/result',
        title: '考核结果查看',
      },
      {
        path: '/leaderExamine/eval',
        name: 'eval',
        authority: 'M_LEADER_EXAMINE_AUTH_EVAL_SEE',
        component: './leaderExamine/eval',
        title: '审核评鉴',
      },
      {
        path: '/leaderExamine/level',
        name: 'level',
        authority: 'M_LEADER_EXAMINE_LEVEL_EVAL_SEE',
        component: './leaderExamine/level',
        title: '等次评议',
      },
    ],
  },
  // 模拟演练
  {
    path: '/simulationExercise',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['ML_DRILL_ROLE_SEE', 'ML_DRILL_TASK_SEE'],
    routes: [
      {
        path: '/simulationExercise/practiceTask',
        name: 'practiceTask',
        component: './simulationExercise/practiceTask',
        authority: 'ML_DRILL_TASK_SEE',
        title: '演练任务',
      },
      {
        path: '/simulationExercise/practiceRole',
        name: 'practiceRole',
        component: './simulationExercise/practiceRole',
        authority: 'ML_DRILL_ROLE_SEE',
        title: '演练角色',
      },
    ],
  },
  {
    path: '/assets',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: [
      'M_ASSET_MANAGE_SEE',
      'M_ASSET_MY_ASSET_SEE',
      'M_ASSET_MY_APPLY_SEE',
      'M_ASSET_AUDIT_SEE',
      'M_ASSET_LIST_SEE',
      'M_ASSET_VIDEO_SEE',
    ],
    routes: [
      {
        path: '/assets/manage',
        name: 'assetsManage',
        component: './assets/manage',
        authority: 'M_ASSET_MANAGE_SEE',
        title: '资产管理',
      },
      {
        path: '/assets/self',
        name: 'assetsSelf',
        component: './assets/self',
        authority: ['M_ASSET_MY_ASSET_SEE', 'M_ASSET_MY_APPLY_SEE'],
        title: '我的资产',
      },
      {
        path: '/assets/check',
        name: 'assetsCheck',
        component: './assets/check',
        authority: 'M_ASSET_AUDIT_SEE',
        title: '我的审批',
      },
      {
        path: '/assets/decision',
        name: 'assetsDecision',
        component: './assets/decision',
        authority: 'M_ASSET_LIST_SEE',
        title: '决策辅助',
      },
      {
        path: '/assets/monitor',
        name: 'assetsMonitor',
        component: './assets/monitor',
        authority: 'M_ASSET_VIDEO_SEE',
        title: '库房监控',
      },
      {
        path: '/assets/transfer',
        name: 'assetsTransfer',
        component: './assets/transfer',
        authority: 'M_ASSET_TRAN_SEE',
        title: '中转资产',
      },
    ],
  },
  {
    // 食堂点餐
    path: '/dinner',
    component: '../layouts/DinnerLayout',
    title: '食堂点餐',
    authority: ['M_DINNER', 'M_DINNER_ORDER', 'M_DINNER_MANAGEMENT'],
    routes: [
      {
        // 我要点餐
        path: '/dinner/order',
        name: 'order',
        component: './dinner/order',
        authority: 'M_DINNER_ORDER',
        meta: {
          name: '我要点餐',
        },
      },
      {
        // 后台管理
        path: '/dinner/management',
        name: 'management',
        component: './dinner/management',
        authority: 'M_DINNER_MANAGEMENT',
        meta: {
          name: '后台管理',
        },
      },
      {
        component: '404',
      },
    ],
  },
  // 干部请销假
  {
    name: 'leaderLeave',
    path: '/leaderLeave',
    title: '干部请销假',
    component: '../layouts/TeamLayout',
    authority: [
      'WEB_LEADER_LEAVE_SEE',
      'WEB_LEADER_APPLY_SEE',
      'WEB_LEADER_AUDIT_SEE',
      'WEB_LEADER_BATCH_SEE',
    ],
    routes: [
      {
        path: '/leaderLeave/manage',
        name: 'leaderLeaveManage',
        component: './leaderLeave/manage',
        authority: 'WEB_LEADER_LEAVE_SEE',
      },
      {
        path: '/leaderLeave/application',
        name: 'leaderLeaveApplication',
        component: './leaderLeave/application',
        authority: 'WEB_LEADER_APPLY_SEE',
      },
      {
        path: '/leaderLeave/audit',
        name: 'leaderLeaveAudit',
        component: './leaderLeave/audit',
        authority: 'WEB_LEADER_AUDIT_SEE',
      },
      {
        path: '/leaderLeave/approval',
        name: 'leaderLeaveApproval',
        component: './leaderLeave/approval',
        authority: 'WEB_LEADER_BATCH_SEE',
      },
    ],
  },
  // 全媒体中心
  {
    name: 'mediaCenter',
    path: '/mediaCenter',
    Routes: ['src/pages/Authorized'],
    authority: 'LARGE_DATA_SCREEN_SEE',
    component: './mediaCenter',
  },
  {
    name: 'mediaCenter',
    path: '/omniMediaCenter',
    title: '全媒体中心',
    component: '../layouts/OmniMediaLayout',
    authority: [
      'LARGE_DATA_SCREEN_SEE',
      'ARTICLE_COLLECTION_SEE',
      'PUBLIC_OPINION_MONITORING_SEE',
      'NEWS_CLUES_SEE',
      'RANDOM_CLAP_SEE',
    ],
    routes: [
      {
        path: '/omniMediaCenter/dataScreen',
        name: 'mediaCenter',
        authority: 'LARGE_DATA_SCREEN_SEE',
        redirect: '/mediaCenter',
      },
      {
        path: '/omniMediaCenter/article',
        name: 'article',
        authority: 'ARTICLE_COLLECTION_SEE',
        component: './omniMediaCenter/article',
      },
      {
        path: '/omniMediaCenter/opinion',
        name: 'opinion',
        authority: 'PUBLIC_OPINION_MONITORING_SEE',
        component: './omniMediaCenter/opinion',
      },
      {
        path: '/omniMediaCenter/clues',
        name: 'clues',
        authority: 'NEWS_CLUES_SEE',
        component: './omniMediaCenter/clues',
      },
      {
        path: '/omniMediaCenter/snapshot',
        name: 'snapshot',
        authority: 'RANDOM_CLAP_SEE',
        component: './omniMediaCenter/snapshot',
      },
    ],
  },
  {
    // 消防学苑
    path: '/exam',
    component: '../layouts/DinnerLayout',
    title: '消防学苑',
    authority: [
      'EXAM_SYSTEM_LEARN_SEE',
      'EXAM_SYSTEM_PRACTICE_SEE',
      'TRAIN_EXAM_SYSTEM_BANK_SEE',
      'TRAIN_EXAM_SYSTEM_PAPER_SEE',
      'TRAIN_EXAM_SYSTEM_TASK_SEE',
    ],
    routes: [
      {
        // 学习中心
        path: '/exam/study',
        name: 'study',
        authority: 'EXAM_SYSTEM_LEARN_SEE',
        component: './exam/study',
      },
      {
        // 练习中心
        path: '/exam/practice',
        name: 'practice',
        authority: 'EXAM_SYSTEM_PRACTICE_SEE',
        component: './exam/practice',
      },
      {
        // 试题库
        path: '/exam/itemBank',
        name: 'itemBank',
        authority: 'TRAIN_EXAM_SYSTEM_BANK_SEE',
        component: './exam/itemBank',
      },
      {
        // 试卷库
        path: '/exam/paper',
        name: 'paper',
        authority: 'TRAIN_EXAM_SYSTEM_PAPER_SEE',
        component: './exam/paper',
      },
      {
        // 考试中心
        path: '/exam/task',
        name: 'task',
        authority: 'TRAIN_EXAM_SYSTEM_TASK_SEE',
        component: './exam/task',
      },
      {
        component: '404',
      },
    ],
  },
  {
    // 试卷详情
    path: '/paperDetail/:type/:id/:userId?',
    component: './exam/paperDetail',
    hideInMenu: true,
  },
  {
    path: '/iframe/team/fireUser/options/:id?',
    name: 'optionsFireUser',
    component: './team/fireUser/optionsFireUser',
    hideInMenu: true,
  },
  {
    path: '/iframe/team/fireUser/:detail/:id',
    name: 'optionsFireUser',
    component: './team/fireUser/optionsFireUser',
    hideInMenu: true,
  },
  {
    path: '/iframe/team/fireUser/:type',
    name: 'optionsFireUser',
    component: './team/fireUser/optionsFireUser',
    hideInMenu: true,
  },
  // 训练管理
  {
    path: '/train',
    name: 'train',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        path: '/train/subjectManagement',
        name: 'subjectManagement',
        authority: 'TRAIN_MANAGE_SUBJECT_SEE',
        component: './train/subjectManagement',
        title: '科目管理',
      },
      {
        path: '/train/exam',
        name: 'theoreticalManagement',
        authority: [
          'TRAIN_EXAM_SYSTEM_BANK_SEE',
          'TRAIN_EXAM_SYSTEM_PAPER_SEE',
          'TRAIN_EXAM_SYSTEM_TASK_SEE',
          'EXAM_KNOWLEDGE_BASE_SEE',
        ],
        component: './redirect.jsx',
        title: '理论管理',
        routes: [
          {
            path: '/train/exam/knowledgeBase',
            name: 'trainExamKnowledgeBase',
            authority: 'EXAM_KNOWLEDGE_BASE_SEE',
            component: './train/knowledgeBase',
            title: '理论知识',
          },
          {
            path: '/train/exam/itemBank',
            name: 'trainExamItemBank',
            authority: ['TRAIN_EXAM_SYSTEM_BANK_SEE'],
            component: './exam/itemBank',
            title: '理论试题',
          },
          {
            path: '/train/exam/paper',
            name: 'trainExamItemPaper',
            authority: ['TRAIN_EXAM_SYSTEM_PAPER_SEE'],
            component: './exam/paper',
            title: '理论试卷',
          },
          {
            path: '/train/exam/task',
            name: 'trainExamItemTask',
            authority: ['TRAIN_EXAM_SYSTEM_TASK_SEE'],
            component: './exam/task',
            title: '理论考试',
          },
        ],
      },
      {
        path: '/train/practical',
        name: 'practicalmanagement',
        authority: [
          'TRAIN_MANAGE_TRAIN_SEE',
          'TRAIN_MANAGE_TRAIN_RECEIVE_PLAN_SEE',
          'TRAIN_MANAGE_TRAIN_ALL_PLAN_SEE',
          'TRAIN_MANAGE_EXAMINE_SEE',
          'TRAIN_MANAGE_USER_SEE',
        ],
        component: './redirect.jsx',
        title: '实操管理',
        routes: [
          {
            path: '/train/practical/trainingPlan',
            name: 'trainPracticalTrainingPlan',
            authority: [
              'TRAIN_MANAGE_TRAIN_SEE',
              'TRAIN_MANAGE_TRAIN_RECEIVE_PLAN_SEE',
              'TRAIN_MANAGE_TRAIN_ALL_PLAN_SEE',
            ],
            component: './train/trainingPlan',
            title: '训练计划',
          },
          {
            path: '/train/practical/examinePlan',
            name: 'trainPracticalExaminePlan',
            authority: 'TRAIN_MANAGE_EXAMINE_SEE',
            component: './train/examinePlan',
            title: '考核计划',
          },
          {
            path: '/train/practical/capabilityAssessment',
            name: 'trainPracticalCapabilityAssessment',
            authority: 'TRAIN_MANAGE_USER_SEE',
            component: './train/capabilityAssessment',
            title: '人才能力评估',
          },
        ],
      },
      {
        path: '/train/dataStatistics',
        name: 'trainDataStatistics',
        authority: ['TRAIN_DATA_STATISTICS_PERSONAL_SEE', 'TRAIN_DATA_STATISTICS_DEPT_SEE'],
        component: './redirect.jsx',
        title: '数据统计',
        routes: [
          {
            path: '/train/dataStatistics/personalData',
            name: 'trainDataStatisticsPersonal',
            authority: ['TRAIN_DATA_STATISTICS_PERSONAL_SEE'],
            component: './train/dataStatistics/personalData',
            title: '个人数据查询',
          },
          {
            path: '/train/dataStatistics/institutionalData',
            name: 'trainDataStatisticsInstitutional',
            authority: ['TRAIN_DATA_STATISTICS_DEPT_SEE'],
            component: './train/dataStatistics/institutionalData',
            title: '机构数据查询',
          },
        ],
      },
      {
        path: '/train/certificates',
        name: 'trainCertificates',
        authority: [
          'TRAIN_CERT_MANAGE_THEORY_SEE',
          'TRAIN_CERT_MANAGE_TRAIN_SEE',
          'TRAIN_CERT_MANAGE_POST_SEE',
          'TRAIN_CERT_MANAGE_BASE_SEE',
        ],
        component: './redirect.jsx',
        title: '证书管理',
        routes: [
          {
            path: '/train/certificates/theoreticalCertificate',
            name: 'trainCertificatesTheoretical',
            authority: ['TRAIN_CERT_MANAGE_THEORY_SEE'],
            component: './train/certificates/theoreticalCertificate',
            title: '理论证书查询',
          },
          {
            path: '/train/certificates/trainingCertificate',
            name: 'trainCertificatesTraining',
            authority: ['TRAIN_CERT_MANAGE_TRAIN_SEE'],
            component: './train/certificates/trainingCertificate',
            title: '培训证书查询',
          },
          {
            path: '/train/certificates/jobCertificate',
            name: 'trainCertificatesJob',
            authority: ['TRAIN_CERT_MANAGE_POST_SEE'],
            component: './train/certificates/jobCertificate',
            title: '上岗证书查询',
          },
          {
            path: '/train/certificates/basicSettings',
            name: 'trainCertificatesBasicSettings',
            authority: ['TRAIN_CERT_MANAGE_BASE_SEE'],
            component: './train/certificates/basicSettings',
            title: '证书基础设置',
          },
        ],
      },
    ],
  },
  // 地址库
  {
    path: '/addressLibrary',
    component: '../layouts/AddressLibraryLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['LEVEL_MANAGE_SEE', 'DEVICE_POINT_MANAGE_SEE'],
    routes: [
      {
        authority: ['LEVEL_MANAGE_SEE', 'DEVICE_POINT_MANAGE_SEE'],
        path: '/addressLibrary/map',
        name: 'addressLibraryMap',
        component: './addressLibrary',
      },
    ],
  },
  {
    path: '/dutyHomepage',
    component: '../layouts/GenericLayout',
    authority: ['DUTY_HOMEPAGE_SEE'],
    routes: [
      {
        title: '值班首页',
        path: '/dutyHomepage/main',
        name: 'dutyHomepage',
        authority: ['DUTY_HOMEPAGE_SEE'],
        component: './dutyHomepage',
      },
    ],
  },
  {
    path: '/alarmReview',
    component: '../layouts/ShellLayout',
    authority: ['ALARM_REPLAY_LIST_SEE'],
    routes: [
      {
        title: '战评复盘',
        path: '/alarmReview/main',
        name: 'alarmReview',
        authority: ['ALARM_REPLAY_LIST_SEE'],
        component: './alarmReview',
      },
    ],
  },
  {
    path: '/addressBook',
    component: '../layouts/ShellLayout',
    authority: ['BOOK_MANAGE_SEE'],
    routes: [
      {
        title: '美兰机场接处警·通讯录管理',
        path: '/addressBook/main',
        name: 'addressBook',
        authority: ['BOOK_MANAGE_SEE'],
        component: './addressBook',
      },
    ],
  },
  {
    path: '/hotWork',
    component: '../layouts/TeamLayout',
    Routes: ['src/pages/Authorized'],
    authority: ['ML_HOT_WORK_SEE'],
    routes: [
      {
        path: '/hotWork/mian/list',
        name: 'hotWorkIndex',
        authority: ['ML_HOT_WORK_SEE'],
        component: './hotWork/hotWorkList',
        title: '动火作业',
      },
    ],
  },
  {
    path: '/hotWorkH5',
    name: '/hotWorkH5',
    routes: [
      {
        path: '/hotWorkH5/h5',
        name: 'hotWorkH5Index',
        component: './hotWork/hotWorkH5',
        title: '动火作业',
      },
      {
        component: './h5/404',
      },
    ],
  },
  {
    path: '/demo',
    component: './user/demo',
  },
  {
    path: '/',
    component: '../layouts/BlankLayout',
    routes: [
      {
        path: '/user',
        component: '../layouts/NewLoginLayout',
        routes: [
          {
            path: '/user',
            redirect: '/user/login',
          },
          {
            name: 'login',
            path: '/user/login',
            component: './user/login',
          },
          {
            component: '404',
          },
        ],
      },
    ],
  },
];

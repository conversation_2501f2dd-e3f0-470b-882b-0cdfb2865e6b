<svg width="85" height="54" viewBox="0 0 85 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<circle cx="42.598" cy="27" r="24" fill="#014176"/>
<circle cx="42.598" cy="27" r="23.5" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter1_d)">
<path d="M62.397 46.7991C64.997 44.1991 67.0595 41.1124 68.4666 37.7153C69.8737 34.3182 70.598 30.6772 70.598 27.0002C70.598 23.3231 69.8737 19.6821 68.4666 16.285C67.0595 12.8879 64.997 9.80121 62.397 7.20117" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter2_d)">
<path d="M22.7989 7.20085C20.1989 9.80089 18.1364 12.8876 16.7293 16.2847C15.3222 19.6818 14.5979 23.3228 14.5979 26.9998C14.5979 30.6769 15.3222 34.3179 16.7293 37.715C18.1364 41.1121 20.1989 44.1988 22.799 46.7988" stroke="#00C2FF"/>
</g>
<g filter="url(#filter3_d)">
<path d="M24 27L19.5 29.5981L19.5 24.4019L24 27Z" fill="#00C2FF"/>
</g>
<g filter="url(#filter4_d)">
<path d="M62 27L66.5 29.5981L66.5 24.4019L62 27Z" fill="#00C2FF"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.525 18.901V16.8008C40.525 16.5937 40.6929 16.4258 40.9 16.4258H53.2C53.4071 16.4258 53.575 16.5937 53.575 16.8008V30.9008C53.575 31.1079 53.4071 31.2758 53.2 31.2758H51.0999V20.401C51.0999 19.5726 50.4284 18.901 49.5999 18.901H40.525ZM33.7 23.625H46C46.2024 23.625 46.3673 23.7854 46.3747 23.986C46.3749 23.9908 46.375 23.9957 46.375 24.0006V34.8754H46.375V38.1C46.375 38.3071 46.2071 38.475 46 38.475H33.7C33.4928 38.475 33.325 38.3071 33.325 38.1V24C33.325 23.7929 33.4928 23.625 33.7 23.625ZM37.3 20.0254H49.6C49.8071 20.0254 49.975 20.1933 49.975 20.4004V34.5004C49.975 34.7075 49.8071 34.8754 49.6 34.8754H47.5V24.0006C47.5 23.1722 46.8284 22.5006 46 22.5006H36.925V20.4004C36.925 20.1933 37.0929 20.0254 37.3 20.0254Z" fill="url(#paint0_linear)"/>
<defs>
<filter id="filter0_d" x="16.598" y="1" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="40.598" y="4.84766" width="44.3051" height="44.3051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="0.292847" y="4.84766" width="44.3051" height="44.3051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="17.5" y="22.4023" width="8.5" height="9.19615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter4_d" x="60" y="22.4023" width="8.5" height="9.19615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="43.45" y1="16.4258" x2="43.45" y2="38.475" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
</defs>
</svg>

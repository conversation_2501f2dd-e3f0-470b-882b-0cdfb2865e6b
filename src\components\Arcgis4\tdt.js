import WebTileLayer from '@arcgis/core/layers/WebTileLayer';
import SpatialReference from '@arcgis/core/geometry/SpatialReference';
import Extent from '@arcgis/core/geometry/Extent';
import tileInfo from './tdtconfig';
import { TILEURL } from '@/constants/global.js';

function tdtLayer(type) {
  let url = `http://t0.tianditu.gov.cn/${type}_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${type}&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={level}&TileRow={row}&TileCol={col}&format=tiles&tk=23d72a42c8757587d2bccfd4ca5fc817`;
  if (TILEURL && TILEURL != 'undefined') {
    if (TILEURL?.startsWith('//')) {
      url = location.protocol + TILEURL?.replace(/\$\{type\}/g, type);
    } else if (TILEURL?.startsWith('/')) {
      url = location.origin + TILEURL.replace(/\$\{type\}/g, type);
    } else {
      url = TILEURL?.replace(/\$\{type\}/g, type);
    }
    // url = TILEURL?.replace("${type}",type)
  }
  // const url = `http://*************:9000/files/map/tile/xm${type}_w/{level}/{col}/{row}.jpg`;// 非代理离线测试
  // const url = `//${location.host}/outLineMap/${type}/{level}/{col}/{row}.jpg`;//代理离线测试
  const tiledLayer = new WebTileLayer({
    urlTemplate: url,
    subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
    spatialReference: new SpatialReference({
      wkid: 102113,
    }),
    fullExtent: new Extent({
      xmax: 20038088.3427892,
      xmin: -20038088.3427892,
      ymax: 20038088.3427892,
      ymin: -20038088.3427892,
      spatialReference: { wkid: 102113 },
    }),
    initialExtent: new Extent({
      xmax: 13211664.142019678,
      xmin: 13077211.409266088,
      ymax: 2843822.418908416,
      ymin: 2810801.622689228,
      spatialReference: { wkid: 102113 },
    }),
    tileInfo: tileInfo.tileInfo,
  });
  return tiledLayer;
}

export { tdtLayer };

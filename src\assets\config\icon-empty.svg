<svg width="386" height="176" viewBox="0 0 386 176" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M193 72.4209C86.409 72.4209 0 108.238 0 152.421H386C386 108.238 299.591 72.4209 193 72.4209Z" fill="url(#paint0_linear)"/>
<path d="M276.074 109.895C276.074 125.475 252.781 138.106 224.048 138.106C195.314 138.106 185.867 131.791 185.867 116.211C185.867 100.631 195.314 81.6846 224.048 81.6846C252.781 81.6846 276.074 94.3149 276.074 109.895Z" fill="url(#paint1_linear)"/>
<path d="M194.979 28.2451L148.806 54.9945L148.806 103.936L194.979 77.1865L194.979 28.2451Z" fill="url(#paint2_linear)"/>
<path d="M194.979 28.2451L241.151 54.9945L241.151 103.936L194.979 77.1865L194.979 28.2451Z" fill="url(#paint3_linear)"/>
<path d="M156.917 90.9462L195.517 69.8936L238.732 93.0515L195.517 113.262L156.917 90.9462Z" fill="#8D9CB0"/>
<path d="M148.807 54.9941L194.979 81.7435L194.979 133.736L148.807 106.987L148.807 54.9941Z" fill="url(#paint4_linear)"/>
<path d="M172.86 105.263L186.286 113.041L186.286 122.513L172.86 114.734L172.86 105.263Z" fill="url(#paint5_linear)"/>
<path d="M241.148 54.9941L194.976 81.7435L194.976 132.894L241.148 106.145L241.148 54.9941Z" fill="url(#paint6_linear)"/>
<path opacity="0.3" d="M234.956 79.999L202.23 98.9586L202.23 122.832L234.956 103.872L234.956 79.999Z" fill="url(#paint7_linear)"/>
<path opacity="0.3" d="M232.439 92.6309L214.818 102.84L214.818 105.838L232.439 95.6294L232.439 92.6309Z" fill="url(#paint8_linear)"/>
<path opacity="0.3" d="M232.439 99.1719L205.587 114.728L205.587 117.667L232.439 102.111L232.439 99.1719Z" fill="url(#paint9_linear)"/>
<g filter="url(#filter0_i)">
<path d="M134.681 75.7893L148.906 54.79L195.557 81.5394L183.77 104L134.681 75.7893Z" fill="url(#paint10_linear)"/>
</g>
<path d="M257.193 71.5781L241.141 55.0566L246.704 68.2096L257.193 71.5781Z" fill="#E0E8F3"/>
<g filter="url(#filter1_i)">
<path d="M249.642 75.7886L241.142 55.0566L195.291 81.5385L202.65 103.999L249.642 75.7886Z" fill="url(#paint11_linear)"/>
</g>
<path d="M194.446 55.5479L202.657 60.3045L202.657 69.8178L194.446 65.0612L194.446 55.5479Z" fill="url(#paint12_linear)"/>
<rect width="9.48882" height="9.51333" transform="matrix(0.865279 -0.501291 2.19548e-08 1 180.761 12.7383)" fill="#D2DBE7"/>
<rect width="9.48882" height="9.51333" transform="matrix(0.865279 -0.501291 2.19548e-08 1 186.688 60.3047)" fill="#D2DBE7"/>
<g filter="url(#filter2_i)">
<rect width="9.48906" height="9.48906" transform="matrix(0.865279 0.501291 -0.865279 0.501291 194.729 55.5479)" fill="url(#paint13_linear)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M216 29.132C216.686 33.528 216.334 37.4684 214.988 40.4547C213.873 42.9277 212.117 44.6545 209.886 45.4989L202.8 49.604L202.658 49.5216V55.3508L194.447 60.2104L194.447 31.7657L198.552 34.1441C200.447 35.2416 202.299 35.6636 203.874 35.3567C204.035 35.3254 204.192 35.2866 204.345 35.2406L194.232 10.6363C192.763 10.6456 191.507 11.204 190.588 12.2648C189.535 13.4802 188.974 15.3006 188.974 17.4957L180.763 12.7391C180.763 10.4647 181.088 8.40702 181.714 6.64016L178.387 5.59852L185.438 1.5203C185.791 1.2789 186.16 1.06564 186.544 0.880959L186.601 0.847961L186.607 0.850972C188.112 0.142426 189.848 -0.130548 191.745 0.0579889C194.995 0.381094 198.572 2.04512 202.023 4.83963C205.474 7.63415 208.644 11.4336 211.131 15.7577C213.619 20.0817 215.314 24.736 216 29.132Z" fill="url(#paint14_linear)"/>
<g filter="url(#filter3_i)">
<path d="M194.476 31.7704L203.305 36.6807L194.896 41.6044L186.723 36.8487L194.476 31.7704Z" fill="url(#paint15_linear)"/>
</g>
<rect width="9.48882" height="9.51333" transform="matrix(0.865279 -0.501291 2.19548e-08 1 180.761 12.7383)" fill="url(#paint16_linear)"/>
<g filter="url(#filter4_i)">
<path d="M207.061 45.1809C208.406 42.1969 208.758 38.2596 208.072 33.867C207.386 29.4744 205.693 24.8238 203.207 20.5031C200.721 16.1825 197.554 12.386 194.105 9.59366C190.657 6.80133 187.083 5.13861 183.835 4.81576C180.587 4.4929 177.81 5.52442 175.857 7.77987C173.904 10.0353 172.861 13.4134 172.861 17.4869L181.066 22.2399C181.066 20.0464 181.627 18.2275 182.679 17.013C183.731 15.7985 185.226 15.2431 186.975 15.4169C188.724 15.5908 190.648 16.4861 192.505 17.9896C194.362 19.4932 196.067 21.5375 197.406 23.864C198.744 26.1905 199.656 28.6947 200.025 31.0599C200.395 33.4252 200.205 35.5452 199.481 37.152C198.756 38.7588 197.529 39.7801 195.955 40.0868C194.381 40.3935 192.531 39.9719 190.638 38.8752L187.219 36.8948C186.841 36.676 186.535 36.8534 186.535 37.2908V55.546L194.74 60.2989V50.2016C196.788 50.8254 198.745 50.9759 200.513 50.6313C203.437 50.0616 205.715 48.1649 207.061 45.1809Z" fill="url(#paint17_linear)"/>
<path d="M194.74 65.0164L186.535 60.2634V69.7693L194.74 74.5222V65.0164Z" fill="url(#paint18_linear)"/>
</g>
<path d="M194.729 65.0127L202.941 60.3002L202.941 69.8135L194.729 74.526L194.729 65.0127Z" fill="url(#paint19_linear)"/>
<path d="M99.0635 124.634L88.9492 141.755L99.0635 145.686V124.634Z" fill="url(#paint20_linear)"/>
<path d="M99.0625 124.633L109.177 141.754L99.0625 145.685V124.633Z" fill="url(#paint21_linear)"/>
<path d="M109.898 73.2637L102.374 86.9599L109.898 90.105V73.2637Z" fill="url(#paint22_linear)"/>
<path d="M109.897 73.2627L117.421 86.9589L109.897 90.104V73.2627Z" fill="url(#paint23_linear)"/>
<path d="M284.884 114.527L279.43 125.803L284.884 128.393V114.527Z" fill="url(#paint24_linear)"/>
<path d="M284.885 114.527L290.339 125.803L284.885 128.393V114.527Z" fill="url(#paint25_linear)"/>
<path d="M332.715 85.6621L324.743 100.903L332.715 104.402V85.6621Z" fill="url(#paint26_linear)"/>
<path d="M332.715 85.6621L340.687 100.903L332.715 104.402V85.6621Z" fill="url(#paint27_linear)"/>
<g opacity="0.5">
<path d="M326.156 160.843L319.708 173.17L326.156 176V160.843Z" fill="url(#paint28_linear)"/>
<path d="M326.155 160.842L332.603 173.169L326.155 175.999V160.842Z" fill="url(#paint29_linear)"/>
</g>
<defs>
<filter id="filter0_i" x="134.681" y="54.79" width="60.8763" height="49.2098" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.840581"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter1_i" x="195.291" y="55.0566" width="54.3504" height="48.9426" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.840581"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter2_i" x="186.519" y="55.5479" width="16.4214" height="9.51357" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.840581"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter3_i" x="186.723" y="31.7705" width="16.582" height="9.83397" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.840581"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter4_i" x="172.861" y="4.75781" width="35.5524" height="69.7644" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.840581" dy="0.840581"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<linearGradient id="paint0_linear" x1="192.462" y1="63.186" x2="192.462" y2="119.295" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF0F9" stop-opacity="0.57"/>
<stop offset="1" stop-color="#F1F4F9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="263.067" y1="111.579" x2="181.672" y2="111.579" gradientUnits="userSpaceOnUse">
<stop stop-color="#DEE4EB" stop-opacity="0"/>
<stop offset="1" stop-color="#D3DCE8"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="171.892" y1="41.6198" x2="185.816" y2="65.6527" gradientUnits="userSpaceOnUse">
<stop stop-color="#BBC4D0"/>
<stop offset="1" stop-color="#9EABBE"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="218.065" y1="41.6198" x2="196.836" y2="78.2626" gradientUnits="userSpaceOnUse">
<stop stop-color="#CED6E1"/>
<stop offset="1" stop-color="#AEB9C8"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="171.893" y1="68.3688" x2="150.071" y2="106.035" gradientUnits="userSpaceOnUse">
<stop stop-color="#C0C8D2"/>
<stop offset="1" stop-color="#E3EBF5"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="179.573" y1="109.152" x2="175.6" y2="116.01" gradientUnits="userSpaceOnUse">
<stop stop-color="#DEE4EB"/>
<stop offset="1" stop-color="#D3DCE8"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="218.062" y1="68.3688" x2="239.519" y2="105.405" gradientUnits="userSpaceOnUse">
<stop stop-color="#A7B6C9"/>
<stop offset="1" stop-color="#D9DFE7"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="218.593" y1="89.4788" x2="228.948" y2="107.353" gradientUnits="userSpaceOnUse">
<stop stop-color="#CED6E1"/>
<stop offset="1" stop-color="#AEB9C8"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="223.629" y1="97.7354" x2="224.929" y2="99.9804" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0B9C5"/>
<stop offset="1" stop-color="#AEB9C8"/>
</linearGradient>
<linearGradient id="paint9_linear" x1="219.013" y1="106.95" x2="220.288" y2="109.15" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0B9C5"/>
<stop offset="1" stop-color="#AEB9C8"/>
</linearGradient>
<linearGradient id="paint10_linear" x1="171.602" y1="71.1577" x2="160.633" y2="92.1786" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7FAFE"/>
<stop offset="1" stop-color="#E3EAF2"/>
</linearGradient>
<linearGradient id="paint11_linear" x1="216.678" y1="71.3354" x2="228.235" y2="91.2167" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDF2F8"/>
<stop offset="1" stop-color="#E1E6ED"/>
</linearGradient>
<linearGradient id="paint12_linear" x1="194.84" y1="57.8167" x2="198.47" y2="56.1721" gradientUnits="userSpaceOnUse">
<stop stop-color="#96B7E6"/>
<stop offset="1" stop-color="#8EABD2"/>
</linearGradient>
<linearGradient id="paint13_linear" x1="5.75515" y1="3.15615" x2="3.40517" y2="6.79636" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCFDFF"/>
<stop offset="1" stop-color="#F8FAFD"/>
</linearGradient>
<linearGradient id="paint14_linear" x1="197.364" y1="0" x2="197.364" y2="39.4903" gradientUnits="userSpaceOnUse">
<stop stop-color="#DEE4ED"/>
<stop offset="1" stop-color="#B1BBCB"/>
</linearGradient>
<linearGradient id="paint15_linear" x1="197.115" y1="36.353" x2="192.232" y2="38.2826" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCFDFF"/>
<stop offset="1" stop-color="#F8FAFD"/>
</linearGradient>
<linearGradient id="paint16_linear" x1="4.74441" y1="0" x2="4.74441" y2="6.23952" gradientUnits="userSpaceOnUse">
<stop stop-color="#BBC4D0"/>
<stop offset="1" stop-color="#9EABBE"/>
</linearGradient>
<linearGradient id="paint17_linear" x1="190.638" y1="4.75781" x2="190.638" y2="74.5222" gradientUnits="userSpaceOnUse">
<stop stop-color="#EBF0F7"/>
<stop offset="1" stop-color="#D7E0EB"/>
</linearGradient>
<linearGradient id="paint18_linear" x1="190.638" y1="4.75781" x2="190.638" y2="74.5222" gradientUnits="userSpaceOnUse">
<stop stop-color="#EBF0F7"/>
<stop offset="1" stop-color="#D7E0EB"/>
</linearGradient>
<linearGradient id="paint19_linear" x1="198.835" y1="62.6341" x2="201.554" y2="67.3276" gradientUnits="userSpaceOnUse">
<stop stop-color="#BBC4D0"/>
<stop offset="1" stop-color="#9EABBE"/>
</linearGradient>
<linearGradient id="paint20_linear" x1="94.0063" y1="124.634" x2="94.0063" y2="145.686" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF0F9"/>
<stop offset="1" stop-color="#F1F4F9"/>
</linearGradient>
<linearGradient id="paint21_linear" x1="104.12" y1="124.633" x2="104.12" y2="145.685" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4EBF3"/>
<stop offset="1" stop-color="#E1E4E9"/>
</linearGradient>
<linearGradient id="paint22_linear" x1="106.136" y1="73.2637" x2="106.136" y2="90.105" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF0F9"/>
<stop offset="1" stop-color="#F1F4F9"/>
</linearGradient>
<linearGradient id="paint23_linear" x1="113.659" y1="73.2627" x2="113.659" y2="90.104" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4EBF3"/>
<stop offset="1" stop-color="#E1E4E9"/>
</linearGradient>
<linearGradient id="paint24_linear" x1="282.157" y1="114.527" x2="282.157" y2="128.393" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF0F9"/>
<stop offset="1" stop-color="#F1F4F9"/>
</linearGradient>
<linearGradient id="paint25_linear" x1="287.612" y1="114.527" x2="287.612" y2="128.393" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4EBF3"/>
<stop offset="1" stop-color="#E1E4E9"/>
</linearGradient>
<linearGradient id="paint26_linear" x1="328.729" y1="85.6621" x2="328.729" y2="104.402" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF0F9"/>
<stop offset="1" stop-color="#F1F4F9"/>
</linearGradient>
<linearGradient id="paint27_linear" x1="336.701" y1="85.6621" x2="336.701" y2="104.402" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4EBF3"/>
<stop offset="1" stop-color="#E1E4E9"/>
</linearGradient>
<linearGradient id="paint28_linear" x1="322.932" y1="160.843" x2="322.932" y2="176" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF0F9"/>
<stop offset="1" stop-color="#F1F4F9"/>
</linearGradient>
<linearGradient id="paint29_linear" x1="329.379" y1="160.842" x2="329.379" y2="175.999" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4EBF3"/>
<stop offset="1" stop-color="#E1E4E9"/>
</linearGradient>
</defs>
</svg>

/*
 * @Author: Ray
 * @Date: 2024-11-30 14:18:50
 * @Description:
 */
export default {
  esbuild: {},
  targets: {
    ie: 11,
  },
  chainWebpack: (memo, { webpack }) => {
    // if(ENV == 'prod' || ENV == 'test') {
    // memo.plugin('HappyPack').use(HappyPack, [{
    //   id: 'js',
    //   loaders: ['babel-loader'],
    //   threadPool: happyThreadPool,
    // }]);
    memo.merge({
      optimization: {
        minimize: true,
        splitChunks: {
          minSize: 30000,
          minChunks: 2,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendor: {
              name: 'vendors',
              test: /(react|react-dom|react-dom-router|babel-polyfill)/,
              chunks: 'all',
              priority: 100,
            },
            antd: {
              name: 'antd',
              test: /antd/,
              chunks: 'async',
              priority: 90,
            },
            arcgis: {
              name: 'arcgis',
              test: /arcgis/,
              chunks: 'async',
              priority: 90,
            },
            lodash: {
              name: 'lodash',
              test: /lodash/,
              chunks: 'async',
              priority: 90,
            },
            echarts: {
              name: 'echarts',
              test: /echarts/,
              chunks: 'async',
              priority: 90,
            },
            bizcharts: {
              name: 'bizcharts',
              test: /bizcharts/,
              chunks: 'initial',
              priority: 90,
            },
            xlsx: {
              name: 'xlsx',
              test: /xlsx/,
              chunks: 'async',
              priority: 90,
            },
            commons: {
              // 其余同步加载包
              chunks: 'all',
              minChunks: 2,
              name: 'commons',
              priority: 80,
              enforce: true,
            },
          },
        },
      },
    });
  },
};

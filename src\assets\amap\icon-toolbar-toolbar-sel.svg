<svg width="85" height="54" viewBox="0 0 85 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<circle cx="42.598" cy="27" r="24" fill="#014176"/>
<circle cx="42.598" cy="27" r="23.5" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter1_d)">
<path d="M62.397 46.7991C64.997 44.1991 67.0595 41.1124 68.4666 37.7153C69.8737 34.3182 70.598 30.6772 70.598 27.0002C70.598 23.3231 69.8737 19.6821 68.4666 16.285C67.0595 12.8879 64.997 9.80121 62.397 7.20117" stroke="#00C2FF"/>
</g>
<g opacity="0.5" filter="url(#filter2_d)">
<path d="M22.7989 7.20085C20.1989 9.80089 18.1364 12.8876 16.7293 16.2847C15.3222 19.6818 14.5979 23.3228 14.5979 26.9998C14.5979 30.6769 15.3222 34.3179 16.7293 37.715C18.1364 41.1121 20.1989 44.1988 22.799 46.7988" stroke="#00C2FF"/>
</g>
<g filter="url(#filter3_d)">
<path d="M24 27L19.5 29.5981L19.5 24.4019L24 27Z" fill="#00C2FF"/>
</g>
<g filter="url(#filter4_d)">
<path d="M62 27L66.5 29.5981L66.5 24.4019L62 27Z" fill="#00C2FF"/>
</g>
<path d="M31 37.38V19.8353L37.4966 16.5L42.625 19.8353L47.983 17.4144L52.75 19.8353V23.3852C52.3144 23.1836 51.8597 23.016 51.3894 22.8858C51.0174 22.7828 50.6243 22.9558 50.4489 23.2996C50.1791 23.8284 49.6308 24.1876 49 24.1876C48.3693 24.1876 47.821 23.8284 47.5512 23.2996C47.3758 22.9558 46.9827 22.7828 46.6107 22.8858C45.21 23.2735 43.9484 23.9931 42.9155 24.9535C42.6526 25.198 42.5813 25.5859 42.74 25.9079C42.8462 26.1232 42.9063 26.366 42.9063 26.6251C42.9063 27.5226 42.1788 28.2501 41.2813 28.2501L41.2587 28.25C40.8995 28.2448 40.5796 28.4762 40.4719 28.8189C40.2057 29.6663 40.0625 30.5673 40.0625 31.5001C40.0625 32.1158 40.1249 32.7177 40.244 33.2997C40.3273 33.7074 40.7053 33.9862 41.1194 33.9455C41.1724 33.9403 41.2264 33.9376 41.2813 33.9376C42.1788 33.9376 42.9063 34.6652 42.9063 35.5626C42.9063 35.9623 42.763 36.3264 42.5241 36.6096C42.3828 36.7771 42.3201 36.9866 42.3347 37.1912L37.4966 34.0447L31 37.38Z" fill="url(#paint0_linear)"/>
<path d="M49.7907 33.3717C49.5477 33.4745 49.2805 33.5314 49 33.5314C47.8782 33.5314 46.9687 32.622 46.9687 31.5001C46.9687 30.9496 47.1878 30.4502 47.5434 30.0844C47.7313 29.8911 47.9574 29.7351 48.2094 29.6285C48.4524 29.5257 48.7196 29.4689 49 29.4689C50.1219 29.4689 51.0313 30.3783 51.0313 31.5001C51.0313 32.018 50.8375 32.4907 50.5184 32.8495C50.3191 33.0735 50.071 33.2532 49.7907 33.3717Z" fill="url(#paint1_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M51.5085 24.629C51.943 24.7876 52.3583 24.9864 52.75 25.2209C53.0387 25.3937 53.3145 25.586 53.5757 25.7958C53.5059 26.061 53.4688 26.3391 53.4688 26.6251C53.4688 28.2151 54.6104 29.5383 56.1187 29.8199C56.2454 30.3587 56.3125 30.9212 56.3125 31.5001C56.3125 31.7858 56.2962 32.0675 56.2644 32.3441C54.6846 32.5652 53.4688 33.9219 53.4688 35.5626C53.4688 36.082 53.591 36.5738 53.8081 37.0098C53.1988 37.5421 52.5007 37.9746 51.7395 38.2824C51.1624 37.3802 50.1518 36.7814 49 36.7814C47.8482 36.7814 46.8376 37.3802 46.2605 38.2824C45.4993 37.9746 44.8012 37.5421 44.1919 37.0098C44.409 36.5738 44.5312 36.082 44.5312 35.5626C44.5312 33.9219 43.3154 32.5652 41.7356 32.3441C41.7038 32.0675 41.6875 31.7858 41.6875 31.5001C41.6875 30.9212 41.7546 30.3587 41.8813 29.8199C42.2392 29.7531 42.5765 29.6276 42.8828 29.4538C43.8671 28.8953 44.5312 27.8378 44.5312 26.6251C44.5312 26.3391 44.4941 26.061 44.4243 25.7958C44.4531 25.7726 44.4822 25.7496 44.5114 25.7269C45.1054 25.2643 45.7723 24.8917 46.4915 24.629C46.4916 24.629 46.4915 24.629 46.4915 24.629C47.087 25.351 47.9892 25.8126 49 25.8126C49.2527 25.8126 49.4987 25.7838 49.7347 25.7292C50.3606 25.5845 50.917 25.2591 51.3462 24.8115C51.4025 24.7527 51.4567 24.6918 51.5085 24.629ZM47.9112 34.9915C48.0051 35.0208 48.1006 35.0464 48.1976 35.0681C48.4559 35.1259 48.7243 35.1564 49 35.1564C49.5049 35.1564 49.9858 35.0541 50.4232 34.869C50.8976 34.6684 51.3208 34.3705 51.6679 34.0003C52.2809 33.3465 52.6562 32.4671 52.6562 31.5001C52.6562 29.4808 51.0193 27.8439 49 27.8439C48.4952 27.8439 48.0143 27.9462 47.5769 28.1312C47.117 28.3257 46.7051 28.6117 46.3642 28.9663C45.7323 29.6234 45.3438 30.5164 45.3438 31.5001C45.3438 33.1402 46.4236 34.5281 47.9112 34.9915Z" fill="url(#paint2_linear)"/>
<defs>
<filter id="filter0_d" x="16.598" y="1" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="40.598" y="4.84766" width="44.3051" height="44.3051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="0.292847" y="4.84766" width="44.3051" height="44.3051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="17.5" y="22.4023" width="8.5" height="9.19615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter4_d" x="60" y="22.4023" width="8.5" height="9.19615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.88 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="43.6562" y1="16.5" x2="43.6562" y2="38.2824" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="43.6562" y1="16.5" x2="43.6562" y2="38.2824" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="43.6562" y1="16.5" x2="43.6562" y2="38.2824" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFD"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
</defs>
</svg>

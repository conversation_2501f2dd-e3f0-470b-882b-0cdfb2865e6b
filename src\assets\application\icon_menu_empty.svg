<svg width="80" height="68" viewBox="0 0 80 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i)">
<path d="M39.2408 0.0549317L9.47835 17.2976L9.47835 48.8452L39.2408 31.6025L39.2408 0.0549317Z" fill="#2B3F6C"/>
</g>
<path d="M38.8082 0.805577L9.91099 17.5469L9.91099 48.0945L38.8082 31.3532L38.8082 0.805577Z" stroke="#5273C6"/>
<g filter="url(#filter1_i)">
<path d="M38.8679 0.0549317L68.6304 17.2976L68.6304 48.8452L38.8679 31.6025L38.8679 0.0549317Z" fill="#2B3F6C"/>
</g>
<path d="M39.3006 0.805577L68.1978 17.5469L68.1978 48.0945L39.3006 31.3532L39.3006 0.805577Z" stroke="#5273C6"/>
<g filter="url(#filter2_i)">
<path d="M14.3335 40.4722L39.215 26.9016L67.0716 41.8292L39.215 54.857L14.3335 40.4722Z" fill="#2B3F6C"/>
</g>
<path d="M39.2395 54.2935L15.3544 40.4849L39.2171 27.47L65.9563 41.7988L39.2395 54.2935Z" stroke="#5273C6"/>
<mask id="path-7-inside-1" fill="white">
<path d="M9.47839 17.6877C9.47839 17.4116 9.67209 17.3 9.91103 17.4384L38.8083 34.1797C39.0472 34.3182 39.2409 34.6542 39.2409 34.9304L39.2409 51.1876L39.2409 66.9449C39.2409 67.4972 38.8535 67.7205 38.3756 67.4436L10.3437 51.2036C9.86579 50.9267 9.47839 50.2546 9.47839 49.7023L9.47839 17.6877Z"/>
</mask>
<g filter="url(#filter3_i)">
<path d="M9.47839 17.6877C9.47839 17.4116 9.67209 17.3 9.91103 17.4384L38.8083 34.1797C39.0472 34.3182 39.2409 34.6542 39.2409 34.9304L39.2409 51.1876L39.2409 66.9449C39.2409 67.4972 38.8535 67.7205 38.3756 67.4436L10.3437 51.2036C9.86579 50.9267 9.47839 50.2546 9.47839 49.7023L9.47839 17.6877Z" fill="#2B3F6C"/>
</g>
<path d="M9.91103 18.4384L38.8083 35.1797L38.8083 33.1797L9.91103 16.4384L9.91103 18.4384ZM38.3756 66.4436L10.3437 50.2036L10.3437 52.2036L38.3756 68.4436L38.3756 66.4436ZM10.3437 50.2036L10.3437 18.189L8.61311 17.1865L8.61311 49.201L10.3437 50.2036ZM38.3756 34.4291L38.3756 50.6864L40.1062 51.6889L40.1062 35.4317L38.3756 34.4291ZM38.3756 50.6864L38.3756 66.4436L40.1062 67.4462L40.1062 51.6889L38.3756 50.6864ZM10.3437 50.2036L10.3437 50.2036L8.61311 49.201C8.61311 50.3056 9.38791 51.6499 10.3437 52.2036L10.3437 50.2036ZM38.3756 68.4436C39.3314 68.9973 40.1062 68.5508 40.1062 67.4462L38.3756 66.4436L38.3756 66.4436L38.3756 68.4436ZM38.8083 35.1797C38.5693 35.0413 38.3756 34.7052 38.3756 34.4291L40.1062 35.4317C40.1062 34.6032 39.5251 33.595 38.8083 33.1797L38.8083 35.1797ZM9.91103 16.4384C9.19421 16.0231 8.61311 16.358 8.61311 17.1865L10.3437 18.189C10.3437 18.4652 10.15 18.5768 9.91103 18.4384L9.91103 16.4384Z" fill="#5273C6" mask="url(#path-7-inside-1)"/>
<g filter="url(#filter4_i)">
<path d="M24.6106 49.7004L33.265 54.7143L33.265 60.8197L24.6106 55.8058L24.6106 49.7004Z" fill="#2B3F6C"/>
</g>
<path d="M25.0432 50.4511L32.8324 54.9637L32.8324 60.0691L25.0432 55.5565L25.0432 50.4511Z" stroke="#5273C6"/>
<g filter="url(#filter5_i)">
<path d="M68.6292 17.2979L38.8667 34.5405L38.8667 67.5122L68.6292 50.2696L68.6292 17.2979Z" fill="#2B3F6C"/>
</g>
<path d="M68.1965 18.0485L39.2993 34.7898L39.2993 66.7616L68.1965 50.0202L68.1965 18.0485Z" stroke="#5273C6"/>
<path opacity="0.3" d="M63.0153 41.5583L51.6563 48.1391L51.6563 50.0719L63.0153 43.4912L63.0153 41.5583Z" fill="#5273C6"/>
<path opacity="0.3" d="M63.0153 45.7747L45.7064 55.8024L45.7064 57.6967L63.0153 47.6689L63.0153 45.7747Z" fill="#5273C6"/>
<g filter="url(#filter6_i)">
<path d="M0 30.7019L9.16994 17.1658L39.2409 34.4084L31.6428 48.8864L0 30.7019Z" fill="#2B3F6C"/>
</g>
<path d="M38.5743 34.6026L31.4415 48.194L0.715894 30.5366L9.32369 17.8303L38.5743 34.6026Z" stroke="#5273C6"/>
<path d="M78.9714 27.9476L78.8376 28.4293C79.0582 28.4906 79.2922 28.3943 79.4057 28.1954C79.5192 27.9966 79.4831 27.746 79.3181 27.5873L78.9714 27.9476ZM67.954 18.0405L78.6248 28.3079L79.3181 27.5873L68.6473 17.3199L67.954 18.0405ZM79.1053 27.4658L71.8565 25.4516L71.5888 26.4151L78.8376 28.4293L79.1053 27.4658Z" fill="#5273C6"/>
<g filter="url(#filter7_i)">
<path d="M74.1038 30.702L68.6249 17.3381L39.0695 34.4083L43.8132 48.8865L74.1038 30.702Z" fill="#2B3F6C"/>
</g>
<path d="M44.0932 48.1352L73.4782 30.4943L68.3794 18.0573L39.671 34.6383L44.0932 48.1352Z" stroke="#5273C6" stroke-linejoin="round"/>
<defs>
<filter id="filter0_i" x="9.47839" y="0.0549316" width="29.7625" height="48.7902" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter1_i" x="38.8679" y="0.0549316" width="29.7625" height="48.7902" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter2_i" x="14.3335" y="26.9016" width="52.7381" height="27.9553" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter3_i" x="9.47839" y="17.3789" width="29.7625" height="50.1835" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter4_i" x="24.6106" y="49.7004" width="8.65445" height="11.1193" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter5_i" x="38.8667" y="17.2979" width="29.7625" height="50.2144" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter6_i" x="0" y="17.1658" width="39.2409" height="31.7206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter7_i" x="39.0695" y="17.3381" width="35.0342" height="31.5484" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.541838"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
</defs>
</svg>

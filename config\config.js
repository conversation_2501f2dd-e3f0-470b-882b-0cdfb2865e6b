// https://umijs.org/config/
import { defineConfig } from 'umi';
import defaultSettings, { defineParams } from './defaultSettings';
import routes from './router.config';
// const HappyPack = require('happypack');
// const happyThreadPool = HappyPack.ThreadPool({ size: require('os').cpus().length })
// const TerserPlugin = require('terser-webpack-plugin');

import DevConfig from './dev.config';
import ProdConfig from './prod.config';

const { ENV } = process.env;
const arcGisPath = defineParams[ENV || 'dev'].arcGisPath;
const arcGisJSPath = defineParams[ENV || 'dev'].arcGisJSPath;
const resURL = defineParams[ENV || 'dev'].resURL;
const videoPluginPath = defineParams[ENV || 'dev'].videoPluginPath;
const HOST = defineParams[ENV || 'dev'].HOST;
const publicPath = defineParams[ENV || 'dev'].publicPath;
const tokenNamePrefix = defineParams[ENV || 'dev'].tokenNamePrefix;
const outputPath = defineParams[ENV || 'dev'].outputPath;
const arcGisXMview = defineParams[ENV || 'dev'].arcGisXMview;

// const plugins = [
//   [
//     'umi-plugin-react',
//     {
//       antd: true,
//       dva: {
//         hmr: true,
//       },
//       locale: {
//         // default false
//         enable: true,
//         // default zh-CN
//         default: 'zh-CN',
//         // default true, when it is true, will use `navigator.language` overwrite default
//         baseNavigator: true,
//       },
//       dynamicImport: {
//         loadingComponent: '@/components/PageLoading/index',
//         webpackChunkName: true,
//         level: 3,
//       },
//       pwa: pwa
//         ? {
//             workboxPluginMode: 'InjectManifest',
//             workboxOptions: {
//               importWorkboxFrom: 'local',
//             },
//           }
//         : false, // default close dll, because issue https://github.com/ant-design/ant-design-pro/issues/4665
//       // dll features https://webpack.js.org/plugins/dll-plugin/
//       // dll: {
//       //   include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch'],
//       //   exclude: ['@babel/runtime', 'netlify-lambda'],
//       // },
//     },
//   ],
//   [
//     'umi-plugin-pro-block',
//     {
//       moveMock: false,
//       moveService: false,
//       modifyRequest: true,
//       autoAddMenu: true,
//     },
//   ],
// ];

// plugins: [
//   ['umi-plugin-react', {
//     antd:true, //使用antd
//     dva:false  //不使用dva，我们直接用redux
//   }],
// ],
export default defineConfig({
  base: publicPath,
  publicPath: publicPath,
  outputPath,
  hash: true,
  antd: {},
  history: {
    type: 'browser',
  },
  locale: {
    default: 'zh-CN',
    antd: true,
    baseNavigator: true,
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  routes: routes,
  theme: {
    'primary-color': defaultSettings.primaryColor,
  },
  define: {
    arcGisPath, // arcGIS 资源地址
    arcGisJSPath,
    videoPluginPath,
    publicPath: publicPath || '', // 定义全局变量
    HOST: HOST || '', // 定义全局变量
    tokenNamePrefix: tokenNamePrefix,
    arcGisXMview,
    resURL,
  },
  title: false,
  ignoreMomentLocale: true,
  disableDynamicImport: false,
  manifest: {
    basePath: publicPath,
  },
  devtool: 'source-map', //开启source-map
  exportStatic: {},
  ...(ENV === 'prod' ? ProdConfig : DevConfig),
});
